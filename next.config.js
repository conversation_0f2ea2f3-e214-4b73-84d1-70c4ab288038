/** @type {import('next').NextConfig} */

const nextConfig = {
  reactStrictMode: false,
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_SELLERBOT_API_URL: process.env.NEXT_PUBLIC_SELLERBOT_API_URL,
  },
  webpack: (config) => {
    config.resolve.fallback = { fs: false };
    return config;
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${
          process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        }/api/:path*`,
      },
      {
        source: '/ingest/static/:path*',
        destination: 'https://us-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/ingest/:path*',
        destination: 'https://us.i.posthog.com/:path*',
      },
      {
        source: '/ingest/decide',
        destination: 'https://us.i.posthog.com/decide',
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
  images: {
    formats: ['image/webp'],
    domains: [
      'www.equalcollective.com',
      'eq--assets.s3.ap-south-1.amazonaws.com',
      'lh3.googleusercontent.com',
      'jeff.equalcollective.com',
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'www.equalcollective.com',
        port: '',
      },
      {
        protocol: 'https',
        hostname: 'eq--assets.s3.ap-south-1.amazonaws.com',
        port: '',
      },
    ],
  },
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
  // async rewrites() {
  //   return {
  //     beforeFiles: [
  //       {
  //         source: '/:path*',
  //         has: [
  //           {
  //             type: 'host',
  //             value: 'personalization.equalcollective.com',
  //           },
  //         ],
  //         destination: 'https://equalcollective.com/personalisation/:path*',
  //       },
  //     ],
  //   };
  // },
  // async redirects() {
  //   return [
  //     {
  //       source: '/personalisation/:path+',
  //       destination: '/personalisation',
  //       permanent: true,
  //     },
  //   ];
  // },
};

// eslint-disable-next-line no-undef
module.exports = nextConfig;
