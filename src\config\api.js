export const getJeffBaseURL = () => {
  if (process.env.NEXT_PUBLIC_JEFF_API_URL) {
    return process.env.NEXT_PUBLIC_JEFF_API_URL;
  }
  return 'http://localhost:8000';
};

export const getBaseURL = () => {
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  return 'http://localhost:8000';
};

export const getSellerBotBaseURL = () => {
  if (process.env.NEXT_PUBLIC_SELLERBOT_API_URL) {
    return process.env.NEXT_PUBLIC_SELLERBOT_API_URL;
  }
  return 'http://localhost:8001';
};

export const API_ENDPOINTS = {
  LOGIN: '/api/login',
  SIGNUP: '/api/signup',
  CURRENT_USER: '/api/me',
  CSV_UPLOAD: '/api/upload',
  JOBS: '/api/jobs',
  JEFF_CLIENTS: '/api/clients',
  JEFF_AMAZON_AUDIT_REPORT: '/api/get_amazon_audit_report',
  JEFF_ANALYZABLE_CSV_DOWNLOAD: '/api/jobs/download-csv',
  UPDLOADABLE_CSV: '/api/uploadable-zip-csv',
  ANALYZABLE_CSV_ZIP: '/api/analyzable-csv',
  AMAZON_AUDIT_REPORT: '/api/get_amazon_audit_report',
  JEFF_USER: '/api/user',
  CLIENT_CONFIG: '/api/configurations',
  JEFF_UPLOAD_IMAGE: '/api/upload-image',
  JEFF_GET_CAMPAIGN: '/api/fetch-campaigns',
  JEFF_ADD_CAMPAIGN: '/api/add-campaign',
  AB_OPTIONS: '/api/a_b_options',
  LIST_AWS_INSTANCES: '/api/aws/instances',
  START_AWS_INSTANCE: '/api/aws/instances/id/start',
  STOP_AWS_INSTANCE: '/api/aws/instances/id/stop',
  RESTART_AWS_INSTANCE: '/api/aws/instances/id/restart',
  QA_STATS: '/api/jobs/qa-summary',
  GENERATE_GENERIC_GOOGLE_SHEET: '/api/create-google-sheet',

  // SellerBot Lead Management APIs
  SB_UPLOAD_LEAD_CSV: '/api/lead/:type',
  SB_UPLOAD_COMPANY_CSV: '/api/lead/:operation/:type',
  SB_SINGLE_LEAD: '/api/single-lead',
  SB_LEAD_GENERATE: '/api/lead-generate',
  SB_EXPORT_LEADS: '/api/lead/export/:jobIds',
  SB_DELETE_LEAD_JOB: '/api/lead/:jobId',
  SB_EXPORT_INPUT: '/api/lead/export-input/:jobIds',
  SB_LEAD_JOB_STATUS: '/api/lead-job-status/:jobId',
  SB_LIST_JOBS: '/api/lead-jobs',
  SB_EXPORT_JOB_LINKS: '/api/lead/export-links/:jobIds',
  SB_MATCH_COMPANY_DATA: '/api/company-data',

  // SellerBot Review System APIs
  SB_REVIEW_JOBS: '/api/lex/jobs',
  SB_REVIEW_JOB_DOWNLOAD: '/api/review-jobs/:jobId/download',
  SB_REVIEW_JOB_STATUS: '/api/review-job-status/:jobId',
  SB_LIST_REVIEW_JOBS: '/api/lex/jobs',
  SB_EXPORT_REVIEW_JOB_LINKS: '/api/review/export-links/:jobIds',

  // LEX APIs
  LEX_UPLOAD_JOB: '/api/lex/jobs',
  LEX_JOB_DOWNLOAD: '/api/lex/jobs/download-csv/:jobId',
  LEX_ALL_JOB_DOWNLOAD: '/api/lex/jobs/download-all-reviews',
  LEX_REMOVED_REVIEWS: '/api/lex/reviews/removed',

  // Lex Image Generator APIs
  LEX_IMAGE_GEN_JOBS: '/api/lex-image-gen/jobs',
  LEX_IMAGE_GEN_JOB_OUTPUT: '/api/lex-image-gen/output/:id',
  LEX_IMAGE_GEN_VIOLATION_TAGS: '/api/lex-image-gen/violation-tags',
  LEX_ADD_VIOLATION_TAG: '/api/lex/add-violation-tag',
};
