/**
 * Test file for PostHog UUID validation functionality
 * Tests the isValidUUID5 and shouldAllowTracking functions with generated UUIDs
 */

import { isValidUUID5, shouldAllowTracking, trackEvent, trackAuditPageView, trackAuditInteraction } from '../posthog';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';
import crypto from 'crypto';

// UUID Generation Utilities using the uuid library
const MY_NAMESPACE_UUID = '1b671a64-40d5-491e-99b0-da01ff1f3341'; // Test namespace

const generateUUID4 = () => {
  return uuidv4();
};

const generateUUID5 = (name, namespace = MY_NAMESPACE_UUID) => {
  return uuidv5(name, namespace);
};

// UUID8 is not officially standardized yet, so we simulate it
// by creating a valid UUID format but with version 8
const generateUUID8 = (customData = 'test-data-for-uuid8') => {
  const hash = crypto.createHash('sha256');
  hash.update(customData, 'utf8');
  const hashBytes = hash.digest();

  // Set version (8) and variant bits
  hashBytes[6] = (hashBytes[6] & 0x0f) | 0x80; // Version 8
  hashBytes[8] = (hashBytes[8] & 0x3f) | 0x80; // Variant 10

  // Format as UUID string
  const hex = hashBytes.toString('hex');
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    hex.substring(12, 16),
    hex.substring(16, 20),
    hex.substring(20, 32)
  ].join('-');
};

// Helper to create UUID with specific version for testing
const createUUIDWithVersion = (version) => {
  const base = '550e8400-e29b-41d4-a716-************';
  const versionChar = version.toString(16);
  return base.substring(0, 14) + versionChar + base.substring(15);
};

// Mock console.log to avoid noise in tests
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

describe('UUID Generation Tests', () => {
  test('should generate valid UUID5', () => {
    const uuid5 = generateUUID5('test-name');
    expect(uuid5).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-5[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    expect(uuid5.charAt(14)).toBe('5'); // Version 5
  });

  test('should generate valid UUID8', () => {
    const uuid8 = generateUUID8('test-data');
    expect(uuid8).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-8[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    expect(uuid8.charAt(14)).toBe('8'); // Version 8
  });

  test('should generate different UUID5s for different names', () => {
    const uuid5a = generateUUID5('name-a');
    const uuid5b = generateUUID5('name-b');
    expect(uuid5a).not.toBe(uuid5b);
    expect(uuid5a.charAt(14)).toBe('5');
    expect(uuid5b.charAt(14)).toBe('5');
  });

  test('should generate different UUID8s for different data', () => {
    const uuid8a = generateUUID8('data-a');
    const uuid8b = generateUUID8('data-b');
    expect(uuid8a).not.toBe(uuid8b);
    expect(uuid8a.charAt(14)).toBe('8');
    expect(uuid8b.charAt(14)).toBe('8');
  });
});

describe('isValidUUID5 with Generated UUIDs', () => {
  test('should return true for generated UUID5', () => {
    const uuid5 = generateUUID5('test-campaign');
    expect(isValidUUID5(uuid5)).toBe(true);
  });

  test('should return false for generated UUID8', () => {
    const uuid8 = generateUUID8('test-campaign');
    expect(isValidUUID5(uuid8)).toBe(false);
  });

  test('should handle multiple generated UUID5s', () => {
    const uuid5s = [
      generateUUID5('campaign-1'),
      generateUUID5('campaign-2'),
      generateUUID5('campaign-3')
    ];

    uuid5s.forEach(uuid => {
      expect(isValidUUID5(uuid)).toBe(true);
      expect(uuid.charAt(14)).toBe('5');
    });
  });

  test('should handle multiple generated UUID8s', () => {
    const uuid8s = [
      generateUUID8('data-1'),
      generateUUID8('data-2'),
      generateUUID8('data-3')
    ];

    uuid8s.forEach(uuid => {
      expect(isValidUUID5(uuid)).toBe(false);
      expect(uuid.charAt(14)).toBe('8');
    });
  });

  test('should return true for generated UUID4', () => {
    const uuid4 = generateUUID4();
    expect(isValidUUID5(uuid4)).toBe(true);
    expect(uuid4.charAt(14)).toBe('4');
  });

  test('should handle all UUID versions correctly', () => {
    const versions = [1, 2, 3, 4, 5, 6, 7, 8];

    versions.forEach(version => {
      const uuid = createUUIDWithVersion(version);
      const shouldAllow = version !== 8;
      expect(isValidUUID5(uuid)).toBe(shouldAllow);
      expect(uuid.charAt(14)).toBe(version.toString(16));
    });
  });

  test('should return true for empty or null values', () => {
    expect(isValidUUID5('')).toBe(true);
    expect(isValidUUID5(null)).toBe(true);
    expect(isValidUUID5(undefined)).toBe(true);
  });

  test('should return true for non-UUID strings', () => {
    expect(isValidUUID5('not-a-uuid')).toBe(true);
    expect(isValidUUID5('12345')).toBe(true);
    expect(isValidUUID5('campaign-123')).toBe(true);
  });
});

describe('shouldAllowTracking with Generated UUIDs', () => {
  // Mock window.location for tests
  const mockLocation = {
    search: ''
  };

  beforeEach(() => {
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true
    });
  });

  test('should return true when no UUID parameters present', () => {
    mockLocation.search = '?source=web&medium=email';
    expect(shouldAllowTracking()).toBe(true);
  });

  test('should return true with generated UUID5 in searchParams', () => {
    const uuid5 = generateUUID5('test-campaign');
    mockLocation.search = `?uuid=${uuid5}`;
    expect(shouldAllowTracking()).toBe(true);
  });

  test('should return false with generated UUID8 in searchParams', () => {
    const uuid8 = generateUUID8('test-campaign');
    mockLocation.search = `?uuid=${uuid8}`;
    expect(shouldAllowTracking()).toBe(false);
  });

  test('should return true with generated UUID4 in searchParams', () => {
    const uuid4 = generateUUID4();
    mockLocation.search = `?uuid=${uuid4}`;
    expect(shouldAllowTracking()).toBe(true);
  });

  test('should handle multiple UUID parameters with mixed versions', () => {
    const uuid5 = generateUUID5('campaign-5');
    const uuid4 = generateUUID4();
    mockLocation.search = `?uuid=${uuid5}&campaign_id=${uuid4}`;
    expect(shouldAllowTracking()).toBe(true);
  });

  test('should block when any parameter contains UUID8', () => {
    const uuid5 = generateUUID5('campaign-5');
    const uuid8 = generateUUID8('campaign-8');
    mockLocation.search = `?uuid=${uuid5}&campaign_id=${uuid8}`;
    expect(shouldAllowTracking()).toBe(false);
  });

  test('should test all monitored parameter names with UUID8', () => {
    const uuid8 = generateUUID8('test-data');
    const paramNames = ['uuid', 'n_uuid', 'utm_uuid', 'campaign_id', 'campaignId'];

    paramNames.forEach(paramName => {
      mockLocation.search = `?${paramName}=${uuid8}`;
      expect(shouldAllowTracking()).toBe(false);
    });
  });

  test('should test all monitored parameter names with UUID5', () => {
    const uuid5 = generateUUID5('test-campaign');
    const paramNames = ['uuid', 'n_uuid', 'utm_uuid', 'campaign_id', 'campaignId'];

    paramNames.forEach(paramName => {
      mockLocation.search = `?${paramName}=${uuid5}`;
      expect(shouldAllowTracking()).toBe(true);
    });
  });

  test('should work with URLSearchParams object and generated UUIDs', () => {
    const uuid8 = generateUUID8('test-data');
    const params = new URLSearchParams(`?uuid=${uuid8}`);
    expect(shouldAllowTracking(params)).toBe(false);

    const uuid5 = generateUUID5('test-campaign');
    const params2 = new URLSearchParams(`?uuid=${uuid5}`);
    expect(shouldAllowTracking(params2)).toBe(true);
  });

  test('should work with plain object and generated UUIDs', () => {
    const uuid8 = generateUUID8('test-data');
    const params = { uuid: uuid8 };
    expect(shouldAllowTracking(params)).toBe(false);

    const uuid5 = generateUUID5('test-campaign');
    const params2 = { uuid: uuid5 };
    expect(shouldAllowTracking(params2)).toBe(true);
  });

  test('should handle complex scenarios with multiple generated UUIDs', () => {
    // Scenario 1: All valid UUIDs
    const uuid5a = generateUUID5('campaign-a');
    const uuid5b = generateUUID5('campaign-b');
    const uuid4 = generateUUID4();

    const validParams = {
      uuid: uuid5a,
      campaign_id: uuid5b,
      utm_uuid: uuid4,
      other_param: 'non-uuid-value'
    };
    expect(shouldAllowTracking(validParams)).toBe(true);

    // Scenario 2: One UUID8 among valid UUIDs
    const uuid8 = generateUUID8('bad-campaign');
    const invalidParams = {
      uuid: uuid5a,
      campaign_id: uuid8, // This UUID8 should block tracking
      utm_uuid: uuid4
    };
    expect(shouldAllowTracking(invalidParams)).toBe(false);
  });

  test('should return true on error', () => {
    // Test error handling by passing invalid input
    expect(shouldAllowTracking('invalid-input')).toBe(true);
  });

  test('should handle edge cases with generated UUIDs', () => {
    // Empty UUID parameters
    expect(shouldAllowTracking({ uuid: '' })).toBe(true);
    expect(shouldAllowTracking({ uuid: null })).toBe(true);
    expect(shouldAllowTracking({ uuid: undefined })).toBe(true);

    // Non-UUID strings
    expect(shouldAllowTracking({ uuid: 'campaign-123' })).toBe(true);
    expect(shouldAllowTracking({ campaign_id: 'summer-2024' })).toBe(true);

    // Mixed valid and invalid formats
    const uuid5 = generateUUID5('valid-campaign');
    expect(shouldAllowTracking({
      uuid: uuid5,
      campaign_id: 'non-uuid-string',
      other: 'value'
    })).toBe(true);
  });
});

describe('Integration Tests - Tracking Functions with Generated UUIDs', () => {
  // Mock PostHog and window
  const mockPostHog = {
    init: jest.fn(),
    capture: jest.fn(),
    identify: jest.fn(),
    people: {
      set: jest.fn()
    }
  };

  const mockLocation = {
    search: '',
    pathname: '/test-path'
  };

  const mockNavigator = {
    userAgent: 'Test User Agent'
  };

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock window and navigator
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true
    });

    Object.defineProperty(window, 'navigator', {
      value: mockNavigator,
      writable: true
    });

    // Mock document
    Object.defineProperty(document, 'referrer', {
      value: 'https://test-referrer.com',
      writable: true
    });

    // Mock PostHog module
    jest.doMock('posthog-js', () => mockPostHog);
  });

  test('trackEvent should proceed with generated UUID5', () => {
    const uuid5 = generateUUID5('test-event');
    mockLocation.search = `?uuid=${uuid5}`;

    // This should not throw and should proceed normally
    expect(() => {
      trackEvent('test_event', { campaign_id: uuid5 });
    }).not.toThrow();
  });

  test('trackEvent should be blocked with generated UUID8', () => {
    const uuid8 = generateUUID8('test-event');
    mockLocation.search = `?uuid=${uuid8}`;

    // This should not throw but should be blocked
    expect(() => {
      trackEvent('test_event', { campaign_id: uuid8 });
    }).not.toThrow();

    // PostHog capture should not be called
    expect(mockPostHog.capture).not.toHaveBeenCalled();
  });

  test('trackAuditPageView should proceed with generated UUID5', () => {
    const uuid5 = generateUUID5('audit-campaign');
    mockLocation.search = `?campaign_id=${uuid5}`;

    const auditData = {
      campaignId: uuid5,
      userEmail: '<EMAIL>',
      sellerId: 'seller123',
      clientName: 'Test Client',
      companyName: 'Test Company',
      auditSlug: 'test-audit'
    };

    expect(() => {
      trackAuditPageView(auditData);
    }).not.toThrow();
  });

  test('trackAuditPageView should be blocked with generated UUID8', () => {
    const uuid8 = generateUUID8('audit-campaign');
    mockLocation.search = `?campaign_id=${uuid8}`;

    const auditData = {
      campaignId: uuid8,
      userEmail: '<EMAIL>',
      sellerId: 'seller123',
      clientName: 'Test Client',
      companyName: 'Test Company',
      auditSlug: 'test-audit'
    };

    expect(() => {
      trackAuditPageView(auditData);
    }).not.toThrow();

    // PostHog capture should not be called
    expect(mockPostHog.capture).not.toHaveBeenCalled();
  });

  test('trackAuditInteraction should proceed with generated UUID5', () => {
    const uuid5 = generateUUID5('interaction-campaign');
    mockLocation.search = `?utm_uuid=${uuid5}`;

    const interactionData = {
      campaignId: uuid5,
      ctaPosition: 'header',
      ctaText: 'Get Started',
      userEmail: '<EMAIL>',
      sellerId: 'seller123',
      clientName: 'Test Client'
    };

    expect(() => {
      trackAuditInteraction(interactionData);
    }).not.toThrow();
  });

  test('trackAuditInteraction should be blocked with generated UUID8', () => {
    const uuid8 = generateUUID8('interaction-campaign');
    mockLocation.search = `?utm_uuid=${uuid8}`;

    const interactionData = {
      campaignId: uuid8,
      ctaPosition: 'header',
      ctaText: 'Get Started',
      userEmail: '<EMAIL>',
      sellerId: 'seller123',
      clientName: 'Test Client'
    };

    expect(() => {
      trackAuditInteraction(interactionData);
    }).not.toThrow();

    // PostHog capture should not be called
    expect(mockPostHog.capture).not.toHaveBeenCalled();
  });

  test('should handle mixed UUID versions in complex scenario', () => {
    const uuid5 = generateUUID5('valid-campaign');
    const uuid8 = generateUUID8('invalid-campaign');
    const uuid4 = generateUUID4();

    // Test with valid UUIDs only
    mockLocation.search = `?uuid=${uuid5}&campaign_id=${uuid4}`;
    expect(() => {
      trackEvent('mixed_test', {
        primary_uuid: uuid5,
        secondary_uuid: uuid4,
        other_data: 'test'
      });
    }).not.toThrow();

    // Test with UUID8 in URL (should block)
    mockLocation.search = `?uuid=${uuid8}`;
    expect(() => {
      trackEvent('mixed_test_blocked', {
        primary_uuid: uuid5, // Valid UUID5 in properties
        other_data: 'test'
      });
    }).not.toThrow();

    // Test with UUID8 in properties (should block)
    mockLocation.search = `?uuid=${uuid5}`;
    expect(() => {
      trackEvent('mixed_test_blocked_2', {
        campaign_uuid: uuid8, // Invalid UUID8 in properties
        other_data: 'test'
      });
    }).not.toThrow();
  });

  test('should generate consistent UUIDs for same input', () => {
    const name = 'consistent-test';
    const uuid5a = generateUUID5(name);
    const uuid5b = generateUUID5(name);

    // Same input should generate same UUID5
    expect(uuid5a).toBe(uuid5b);
    expect(uuid5a.charAt(14)).toBe('5');

    const data = 'consistent-data';
    const uuid8a = generateUUID8(data);
    const uuid8b = generateUUID8(data);

    // Same input should generate same UUID8
    expect(uuid8a).toBe(uuid8b);
    expect(uuid8a.charAt(14)).toBe('8');
  });

  test('should demonstrate real-world usage patterns', () => {
    // Simulate real campaign scenarios
    const campaigns = [
      { name: 'summer-2024', type: 'valid', expectedVersion: '5' },
      { name: 'winter-promo', type: 'valid', expectedVersion: '5' },
      { name: 'black-friday', type: 'valid', expectedVersion: '5' }
    ];

    const invalidCampaigns = [
      { name: 'test-campaign-1', type: 'invalid', expectedVersion: '8' },
      { name: 'test-campaign-2', type: 'invalid', expectedVersion: '8' }
    ];

    // Test valid campaigns
    campaigns.forEach(campaign => {
      const uuid = generateUUID5(campaign.name);
      expect(uuid.charAt(14)).toBe(campaign.expectedVersion);
      expect(isValidUUID5(uuid)).toBe(true);

      mockLocation.search = `?campaign_id=${uuid}`;
      expect(shouldAllowTracking()).toBe(true);
    });

    // Test invalid campaigns
    invalidCampaigns.forEach(campaign => {
      const uuid = generateUUID8(campaign.name);
      expect(uuid.charAt(14)).toBe(campaign.expectedVersion);
      expect(isValidUUID5(uuid)).toBe(false);

      mockLocation.search = `?campaign_id=${uuid}`;
      expect(shouldAllowTracking()).toBe(false);
    });
  });
});
