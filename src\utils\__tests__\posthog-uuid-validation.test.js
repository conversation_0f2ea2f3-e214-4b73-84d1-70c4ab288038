/**
 * Test file for PostHog UUID validation functionality
 * Tests the isValidUUID5 and shouldAllowTracking functions
 */

import { isValidUUID5, shouldAllowTracking } from '../posthog';

// Mock console.log to avoid noise in tests
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

describe('isValidUUID5', () => {
  test('should return true for valid UUID5', () => {
    const uuid5 = '550e8400-e29b-51d4-a716-************'; // UUID5 (version 5)
    expect(isValidUUID5(uuid5)).toBe(true);
  });

  test('should return false for UUID8', () => {
    const uuid8 = '550e8400-e29b-81d4-a716-************'; // UUID8 (version 8)
    expect(isValidUUID5(uuid8)).toBe(false);
  });

  test('should return true for UUID4', () => {
    const uuid4 = '550e8400-e29b-41d4-a716-************'; // UUID4 (version 4)
    expect(isValidUUID5(uuid4)).toBe(true);
  });

  test('should return true for UUID1', () => {
    const uuid1 = '550e8400-e29b-11d4-a716-************'; // UUID1 (version 1)
    expect(isValidUUID5(uuid1)).toBe(true);
  });

  test('should return true for empty or null values', () => {
    expect(isValidUUID5('')).toBe(true);
    expect(isValidUUID5(null)).toBe(true);
    expect(isValidUUID5(undefined)).toBe(true);
  });

  test('should return true for non-UUID strings', () => {
    expect(isValidUUID5('not-a-uuid')).toBe(true);
    expect(isValidUUID5('12345')).toBe(true);
    expect(isValidUUID5('campaign-123')).toBe(true);
  });

  test('should return true for invalid UUID format', () => {
    expect(isValidUUID5('550e8400-e29b-51d4-a716')).toBe(true); // Too short
    expect(isValidUUID5('550e8400-e29b-51d4-a716-************-extra')).toBe(true); // Too long
  });
});

describe('shouldAllowTracking', () => {
  // Mock window.location for tests
  const mockLocation = {
    search: ''
  };

  beforeEach(() => {
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true
    });
  });

  test('should return true when no UUID parameters present', () => {
    mockLocation.search = '?source=web&medium=email';
    expect(shouldAllowTracking()).toBe(true);
  });

  test('should return true with valid UUID5 in searchParams', () => {
    mockLocation.search = '?uuid=550e8400-e29b-51d4-a716-************';
    expect(shouldAllowTracking()).toBe(true);
  });

  test('should return false with UUID8 in searchParams', () => {
    mockLocation.search = '?uuid=550e8400-e29b-81d4-a716-************';
    expect(shouldAllowTracking()).toBe(false);
  });

  test('should return false with UUID8 in utm_uuid parameter', () => {
    mockLocation.search = '?utm_uuid=550e8400-e29b-81d4-a716-************';
    expect(shouldAllowTracking()).toBe(false);
  });

  test('should return false with UUID8 in n_uuid parameter', () => {
    mockLocation.search = '?n_uuid=550e8400-e29b-81d4-a716-************';
    expect(shouldAllowTracking()).toBe(false);
  });

  test('should return false with UUID8 in campaign_id parameter', () => {
    mockLocation.search = '?campaign_id=550e8400-e29b-81d4-a716-************';
    expect(shouldAllowTracking()).toBe(false);
  });

  test('should work with URLSearchParams object', () => {
    const params = new URLSearchParams('?uuid=550e8400-e29b-81d4-a716-************');
    expect(shouldAllowTracking(params)).toBe(false);
  });

  test('should work with plain object', () => {
    const params = { uuid: '550e8400-e29b-81d4-a716-************' };
    expect(shouldAllowTracking(params)).toBe(false);
  });

  test('should return true on error', () => {
    // Test error handling by passing invalid input
    expect(shouldAllowTracking('invalid-input')).toBe(true);
  });
});
