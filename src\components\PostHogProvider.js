'use client';

import React, { useEffect } from 'react';
import { initPostHog } from '../utils/posthog';

/**
 * PostHog Provider Component
 * Provides backup PostHog initialization for audit-specific tracking
 * Works alongside global PostHog initialization in other providers
 */
export default function PostHogProvider({ children }) {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;
    
    // Only initialize if we're on an audit page and PostHog hasn't been initialized yet
    if (window.location.pathname.includes('/audit/')) {
      console.log('PostHogProvider: Ensuring PostHog is available for audit tracking...');
      
      try {
        // Initialize PostHog for audit pages (fallback if global init failed)
        initPostHog();
        
        // Verify initialization
        setTimeout(() => {
          console.log('PostHog Provider: Audit tracking initialization complete');
          console.log('Environment variables:', {
            hasApiKey: !!process.env.NEXT_PUBLIC_POSTHOG_KEY,
            hasHost: !!process.env.NEXT_PUBLIC_POSTHOG_HOST,
            nodeEnv: process.env.NODE_ENV,
          });
        }, 1000);
        
      } catch (error) {
        console.error('PostHog Provider: Failed to initialize audit tracking:', error);
      }
    }
  }, []);

  return <>{children}</>;
} 