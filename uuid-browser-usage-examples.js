/**
 * UUID Library Browser Usage Examples
 * 
 * This shows how to use the uuid library in different browser environments:
 * - Vanilla JavaScript
 * - React/Next.js
 * - ES6 Modules
 * - CommonJS
 */

console.log('🌐 UUID Library Browser Usage Examples\n');
console.log('=' .repeat(50));

// ===== 1. BROWSER SUPPORT CONFIRMATION =====
console.log('\n📋 Browser Support:');
console.log('✅ Chrome, Safari, Firefox, Edge (latest versions)');
console.log('✅ Mobile browsers (expected to work)');
console.log('✅ Uses crypto.getRandomValues() API');
console.log('✅ No Node.js dependencies in browser');

// ===== 2. INSTALLATION METHODS =====
console.log('\n📦 Installation Methods:');

console.log('\n1. NPM/Yarn (for bundled apps):');
console.log('   npm install uuid');
console.log('   yarn add uuid');

console.log('\n2. CDN (for direct browser use):');
console.log('   <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidv4.min.js"></script>');
console.log('   <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidv5.min.js"></script>');
console.log('   <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidvalidate.min.js"></script>');
console.log('   <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidversion.min.js"></script>');

// ===== 3. USAGE EXAMPLES =====
console.log('\n💻 Usage Examples:');

console.log('\n--- ES6 Modules (React/Next.js) ---');
console.log(`
import { v4 as uuidv4, v5 as uuidv5, validate, version } from 'uuid';

// Generate UUIDs
const uuid4 = uuidv4();
const uuid5 = uuidv5('campaign-name', '1b671a64-40d5-491e-99b0-da01ff1f3341');

// Validate UUIDs
const isValid = validate(uuid4);
const uuidVersion = version(uuid4);

// Your PostHog validation
const isValidUUID5Only = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true;
  if (!validate(uuid)) return true;
  
  try {
    return version(uuid) === 5;
  } catch (error) {
    return true;
  }
};
`);

console.log('\n--- CommonJS (Node.js style) ---');
console.log(`
const { v4: uuidv4, v5: uuidv5, validate, version } = require('uuid');

const uuid5 = uuidv5('campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341');
const isUUID5 = validate(uuid5) && version(uuid5) === 5;
`);

console.log('\n--- CDN/Global Variables ---');
console.log(`
// After loading CDN scripts
const uuid4 = uuidv4();
const uuid5 = uuidv5('campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341');
const isValid = uuidvalidate(uuid5);
const ver = uuidversion(uuid5);
`);

// ===== 4. REACT COMPONENT EXAMPLE =====
console.log('\n⚛️ React Component Example:');
console.log(`
import React, { useState } from 'react';
import { v5 as uuidv5, validate, version } from 'uuid';

const UUIDValidator = () => {
  const [testUuid, setTestUuid] = useState('');
  const [result, setResult] = useState(null);
  
  const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';
  
  const validateUUID = (uuid) => {
    if (!uuid) return { allowed: true, reason: 'Empty UUID' };
    if (!validate(uuid)) return { allowed: true, reason: 'Non-UUID string' };
    
    try {
      const ver = version(uuid);
      if (ver === 5) {
        return { allowed: true, reason: 'Valid UUID5' };
      }
      return { allowed: false, reason: \`UUID\${ver} blocked (only UUID5 allowed)\` };
    } catch (error) {
      return { allowed: true, reason: 'Validation error' };
    }
  };
  
  const generateUUID5 = () => {
    const campaignName = 'test-campaign-' + Date.now();
    const uuid = uuidv5(campaignName, namespace);
    setTestUuid(uuid);
    setResult(validateUUID(uuid));
  };
  
  const handleInputChange = (e) => {
    const uuid = e.target.value;
    setTestUuid(uuid);
    setResult(validateUUID(uuid));
  };
  
  return (
    <div>
      <h3>UUID5-Only Validator</h3>
      <button onClick={generateUUID5}>Generate Valid UUID5</button>
      <input 
        type="text" 
        value={testUuid} 
        onChange={handleInputChange}
        placeholder="Enter UUID to test..."
        style={{ width: '300px', margin: '10px' }}
      />
      {result && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: result.allowed ? '#d4edda' : '#f8d7da',
          border: \`1px solid \${result.allowed ? '#c3e6cb' : '#f5c6cb'}\`,
          borderRadius: '5px'
        }}>
          <strong>Result:</strong> {result.allowed ? '✅ ALLOWED' : '❌ BLOCKED'}<br/>
          <strong>Reason:</strong> {result.reason}
        </div>
      )}
    </div>
  );
};

export default UUIDValidator;
`);

// ===== 5. NEXT.JS POSTHOG INTEGRATION =====
console.log('\n🚀 Next.js PostHog Integration:');
console.log(`
// utils/posthog-browser.js
import { validate, version } from 'uuid';

export const isValidUUID5Only = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true;
  
  // Browser-safe validation
  if (typeof window === 'undefined') return true; // SSR safety
  
  if (!validate(uuid)) {
    return true; // Allow non-UUID strings
  }
  
  try {
    const ver = version(uuid);
    
    if (ver === 5) {
      console.log('Valid UUID5 detected - allowing tracking:', uuid);
      return true;
    }
    
    console.log(\`UUID version \${ver} detected - blocking tracking (only UUID5 allowed):\`, uuid);
    return false;
  } catch (error) {
    console.error('Error checking UUID version:', error);
    return true;
  }
};

export const shouldAllowTracking = (searchParams) => {
  if (typeof window === 'undefined') return true;
  
  try {
    const params = searchParams || new URLSearchParams(window.location.search);
    const uuidParams = ['uuid', 'n_uuid', 'utm_uuid', 'campaign_id', 'campaignId'];
    
    for (const paramName of uuidParams) {
      const paramValue = params.get(paramName);
      if (paramValue && !isValidUUID5Only(paramValue)) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error checking tracking validation:', error);
    return true;
  }
};
`);

// ===== 6. BROWSER COMPATIBILITY NOTES =====
console.log('\n🔧 Browser Compatibility Notes:');

console.log('\n✅ Supported:');
console.log('• Modern browsers with crypto.getRandomValues()');
console.log('• Chrome 11+, Firefox 21+, Safari 6.1+, Edge 12+');
console.log('• Mobile browsers (iOS Safari, Chrome Mobile)');
console.log('• Web Workers and Service Workers');

console.log('\n⚠️ Potential Issues:');
console.log('• Very old browsers (IE < 11) - need polyfill');
console.log('• React Native - needs react-native-get-random-values');
console.log('• Some testing environments - may need jsdom setup');

console.log('\n🔧 Polyfill for Old Browsers:');
console.log(`
// For IE11 and older browsers
if (!window.crypto || !window.crypto.getRandomValues) {
  // Load polyfill or use fallback
  console.warn('crypto.getRandomValues not available');
}
`);

// ===== 7. TESTING IN BROWSER =====
console.log('\n🧪 Testing in Browser:');
console.log(`
// Browser console test
console.log('Testing UUID library in browser...');

// Test generation
const uuid4 = uuidv4();
const uuid5 = uuidv5('test', '1b671a64-40d5-491e-99b0-da01ff1f3341');

console.log('UUID4:', uuid4, 'Version:', uuidversion(uuid4));
console.log('UUID5:', uuid5, 'Version:', uuidversion(uuid5));

// Test validation
console.log('UUID4 valid:', uuidvalidate(uuid4));
console.log('UUID5 valid:', uuidvalidate(uuid5));
console.log('Invalid UUID:', uuidvalidate('not-a-uuid'));
`);

// ===== 8. PERFORMANCE NOTES =====
console.log('\n⚡ Performance Notes:');
console.log('• UUID generation is very fast in browsers');
console.log('• validate() and version() are lightweight operations');
console.log('• No network requests - all client-side');
console.log('• Minimal bundle size impact (~10KB gzipped)');
console.log('• Tree-shaking friendly (import only what you need)');

console.log('\n📊 Bundle Size (approximate):');
console.log('• Full uuid library: ~25KB');
console.log('• Individual functions: ~5-10KB each');
console.log('• Validation functions: ~3KB');

console.log('\n' + '=' .repeat(50));
console.log('✨ UUID library works perfectly in browsers!');
console.log('🎯 Your PostHog validation will work client-side');
console.log('🔒 UUID5-only validation is browser-compatible');

// ===== 9. QUICK START FOR YOUR PROJECT =====
console.log('\n🚀 Quick Start for Your Project:');
console.log(`
1. The uuid library is already installed in your project
2. Your PostHog validation will work in the browser
3. Import and use in your React components:

   import { v5 as uuidv5, validate, version } from 'uuid';
   
4. Generate valid UUID5s for testing:
   
   const uuid5 = uuidv5('campaign-name', '1b671a64-40d5-491e-99b0-da01ff1f3341');
   
5. Test in browser console or React DevTools
`);
