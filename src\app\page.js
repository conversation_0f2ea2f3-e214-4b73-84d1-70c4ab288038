import { redirect } from 'next/navigation';

const metaDetails = {
  title: 'AI Employees for Amazon Agencies | Equal Collective',
  description:
    'AI agents that take up critical tasks like Sales, review management, SEO, PPC & Pricing for Amazon. Enabling growth & profitability without growing headcount.',
  image: '/images/home-og2.png',
  url: '/',
};

export const metadata = {
  title: metaDetails.title,
  description: metaDetails.description,
  twitter: {
    card: 'summary_large_image',
    title: metaDetails.title,
    images: [metaDetails.image],
  },
  openGraph: {
    title: metaDetails.title,
    url: metaDetails.url,
    siteName: 'Equal Collective',
    images: [
      {
        url: metaDetails.image,
        width: 800,
        height: 600,
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

const IndexPage = () => {
  redirect('https://equalcollective.com');
};

export default IndexPage;
