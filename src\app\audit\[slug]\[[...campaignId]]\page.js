import React from 'react';
import { notFound } from 'next/navigation';
import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON> from '../../../../views/Jeff/JeffAuditPage/JeffAudit';
import { decodedURL } from '../../../../utils/encodedAuditSlug';
import { API_ENDPOINTS } from '../../../../config/api';

async function JeffAuditPage({ params }) {
  const { slug: auditSlug, campaignId: paramSegments } = params;
  
  try {
    // Parse the URL segments
    // URL format: /audit/product-slug/encoded-email/encoded-seller-id/encoded-client-id/campaign-id
    let campaignId = null;
    let urlSegmentsForDecoding = '';
    
    if (paramSegments && Array.isArray(paramSegments) && paramSegments.length > 0) {
      // Campaign ID should be the last segment IF we have more than the minimum required segments
      // Minimum segments: [encoded-email, encoded-seller-id, encoded-client-id] = 3 segments
      if (paramSegments.length > 3) {
        campaignId = paramSegments[paramSegments.length - 1];
        // Send all but the last segment to the decodedURL function
        urlSegmentsForDecoding = paramSegments.slice(0, -1).join('/');
      } else {
        // If we have 3 or fewer segments, there's no campaign ID
        campaignId = null;
        urlSegmentsForDecoding = paramSegments.join('/');
      }
    }

    // Send all but the last segment to the decodedURL function (if campaign ID exists)
    const parseResult = decodedURL(`/audit/${auditSlug}/${urlSegmentsForDecoding}`);
    
    const { email, sellerId, clientId } = parseResult;

    // Clean and filter the user data - remove ### placeholders and empty values
    const userData = {
      email: email,
      sellerId: sellerId,
      clientId: clientId,
      campaignId: campaignId,
    };
    

    // Structure data correctly for PostHog tracking - matching JeffAudit component expectations
    const originalParams = {
      slug: auditSlug,
      userData: userData,
      isEncoded: paramSegments && paramSegments.length >= 3,
      urlSegments: paramSegments ? paramSegments.length : 0
    };
    
    console.log('Audit URL Processing:', {
      originalSlug: auditSlug,
      actualSlug: auditSlug,
      campaignId: campaignId,
      originalParams: originalParams,
      params: paramSegments,
      parseResult: parseResult
    });

    const baseURL = 'https://api.jeff.equalcollective.com';

    // Call the existing API with the audit slug
    const response = await axios.get(
      `${baseURL}${API_ENDPOINTS.AMAZON_AUDIT_REPORT}/${auditSlug}`,
      {
        timeout: 30000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Jeff-Audit-Frontend/1.0',
        },
      }
    );

    const auditData = response.data;

    return (
      <JeffAudit 
        companyData={auditData} 
        originalParams={originalParams}
      />
    );
  } catch (error) {
    console.error('Failed to fetch audit data:', error);
    console.error('Error details:', {
      originalSlug: auditSlug,
      params: paramSegments,
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data
    });

    if (error.response?.status === 404) {
      notFound();
    }

    throw error;
  }
}

export default JeffAuditPage;
