/**
 * UUID Library Examples for PostHog Validation Testing
 * 
 * This demonstrates how to use the 'uuid' npm package to generate
 * UUID5 and simulate UUID8 for testing the PostHog tracking validation.
 * 
 * Installation: npm install uuid
 * Run: node uuid-library-examples.js
 */

const { v4: uuidv4, v5: uuidv5 } = require('uuid');
const crypto = require('crypto');

// Define a namespace UUID for consistent UUID5 generation
const MY_NAMESPACE_UUID = '1b671a64-40d5-491e-99b0-da01ff1f3341';

console.log('🔧 UUID Library Examples for PostHog Validation\n');
console.log('=' .repeat(60));

// 1. Generate UUID4 (random)
console.log('\n📝 1. UUID4 Generation (Random):');
const uuid4Examples = [];
for (let i = 1; i <= 3; i++) {
  const uuid4 = uuidv4();
  uuid4Examples.push(uuid4);
  console.log(`   ${i}. ${uuid4} (version: ${uuid4.charAt(14)})`);
}

// 2. Generate UUID5 (deterministic, based on name + namespace)
console.log('\n📝 2. UUID5 Generation (Deterministic):');
const campaignNames = [
  'summer-2024-campaign',
  'winter-holiday-promo', 
  'black-friday-sale',
  'spring-product-launch',
  'customer-retention-drive'
];

const uuid5Examples = [];
campaignNames.forEach((name, index) => {
  const uuid5 = uuidv5(name, MY_NAMESPACE_UUID);
  uuid5Examples.push({ name, uuid: uuid5 });
  console.log(`   ${index + 1}. Campaign: "${name}"`);
  console.log(`      UUID5: ${uuid5} (version: ${uuid5.charAt(14)})`);
});

// 3. Simulate UUID8 (not officially standardized, but for testing)
console.log('\n📝 3. UUID8 Simulation (For Testing Blocking):');
const generateUUID8 = (customData) => {
  const hash = crypto.createHash('sha256');
  hash.update(customData, 'utf8');
  const hashBytes = hash.digest();
  
  // Set version (8) and variant bits
  hashBytes[6] = (hashBytes[6] & 0x0f) | 0x80; // Version 8
  hashBytes[8] = (hashBytes[8] & 0x3f) | 0x80; // Variant 10
  
  // Format as UUID string
  const hex = hashBytes.toString('hex');
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    hex.substring(12, 16),
    hex.substring(16, 20),
    hex.substring(20, 32)
  ].join('-');
};

const uuid8Examples = [];
campaignNames.forEach((name, index) => {
  const uuid8 = generateUUID8(name);
  uuid8Examples.push({ name, uuid: uuid8 });
  console.log(`   ${index + 1}. Campaign: "${name}"`);
  console.log(`      UUID8: ${uuid8} (version: ${uuid8.charAt(14)})`);
});

// 4. Demonstrate deterministic nature of UUID5
console.log('\n📝 4. UUID5 Deterministic Behavior:');
const testName = 'test-campaign';
const uuid5_1 = uuidv5(testName, MY_NAMESPACE_UUID);
const uuid5_2 = uuidv5(testName, MY_NAMESPACE_UUID);
console.log(`   Same input "${testName}" generates same UUID5:`);
console.log(`   First:  ${uuid5_1}`);
console.log(`   Second: ${uuid5_2}`);
console.log(`   Match:  ${uuid5_1 === uuid5_2 ? '✅ YES' : '❌ NO'}`);

// 5. Show different namespaces produce different UUID5s
console.log('\n📝 5. Different Namespaces Produce Different UUID5s:');
const namespace1 = '1b671a64-40d5-491e-99b0-da01ff1f3341';
const namespace2 = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';
const sameName = 'identical-campaign-name';

const uuid5_ns1 = uuidv5(sameName, namespace1);
const uuid5_ns2 = uuidv5(sameName, namespace2);

console.log(`   Name: "${sameName}"`);
console.log(`   Namespace 1: ${uuid5_ns1}`);
console.log(`   Namespace 2: ${uuid5_ns2}`);
console.log(`   Different:   ${uuid5_ns1 !== uuid5_ns2 ? '✅ YES' : '❌ NO'}`);

// 6. Test URLs for PostHog validation
console.log('\n📝 6. Example URLs for PostHog Testing:');
console.log('\n   ✅ URLs that ALLOW tracking (UUID5 and UUID4):');
uuid5Examples.slice(0, 2).forEach(({ name, uuid }, index) => {
  console.log(`   ${index + 1}. /audit/product?campaign_id=${uuid}`);
  console.log(`      Campaign: ${name} (UUID5)`);
});

uuid4Examples.slice(0, 1).forEach((uuid, index) => {
  console.log(`   3. /audit/product?uuid=${uuid}`);
  console.log(`      Random UUID4`);
});

console.log('\n   ❌ URLs that BLOCK tracking (UUID8):');
uuid8Examples.slice(0, 2).forEach(({ name, uuid }, index) => {
  console.log(`   ${index + 1}. /audit/product?campaign_id=${uuid}`);
  console.log(`      Campaign: ${name} (UUID8 - BLOCKED)`);
});

// 7. Code examples for your tests
console.log('\n📝 7. Code Examples for Your Tests:');
console.log(`
// Import the uuid library
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

// Define your namespace
const MY_NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';

// Generate UUID5 (deterministic)
const uuid5 = uuidv5('my-campaign-name', MY_NAMESPACE);
console.log(uuid5); // Always same result for same input

// Generate UUID4 (random)
const uuid4 = uuidv4();
console.log(uuid4); // Different every time

// Test your validation
import { isValidUUID5 } from './src/utils/posthog';
console.log(isValidUUID5(uuid5)); // true (allowed)
console.log(isValidUUID5(uuid4)); // true (allowed)
`);

// 8. Export test data
console.log('\n📝 8. Exporting Test Data:');
const testData = {
  uuid4Examples,
  uuid5Examples: uuid5Examples.map(item => item.uuid),
  uuid8Examples: uuid8Examples.map(item => item.uuid),
  namespace: MY_NAMESPACE_UUID,
  campaignNames
};

// Save to file for use in tests
const fs = require('fs');
try {
  fs.writeFileSync('test-uuids.json', JSON.stringify(testData, null, 2));
  console.log('   ✅ Test data saved to test-uuids.json');
} catch (error) {
  console.log('   ❌ Could not save test data:', error.message);
}

console.log('\n' + '=' .repeat(60));
console.log('✨ Examples complete! Use these UUIDs in your tests.');
console.log('\nNext steps:');
console.log('1. npm install uuid');
console.log('2. Use uuidv5() for deterministic UUID5 generation');
console.log('3. Use uuidv4() for random UUID4 generation');
console.log('4. Use the custom generateUUID8() function to test blocking');
console.log('5. Test your PostHog validation with these UUIDs');

// Export functions for use as module
module.exports = {
  generateUUID4: uuidv4,
  generateUUID5: (name, namespace = MY_NAMESPACE_UUID) => uuidv5(name, namespace),
  generateUUID8,
  MY_NAMESPACE_UUID,
  testData
};
