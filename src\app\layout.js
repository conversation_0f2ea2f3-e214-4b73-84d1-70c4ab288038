/* eslint-disable react/display-name */
import React from 'react';
import { Analytics } from '@vercel/analytics/react';

// import Document, { Html, Head, Main, NextScript } from 'next/document';
// import createCache from '@emotion/cache';
// import createEmotionServer from '@emotion/server/create-instance';

//import { GoogleTagManager } from '@next/third-parties/google';
import GoogleAnalytics from 'views/GoogleAnalytics/GoogleAnalytics';
import PostHogProvider from '../components/PostHogProvider';

import Page from '../components/Page';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import 'aos/dist/aos.css';
import { Inter } from 'next/font/google';
import Providers from './provider';

const metaDetails = {
  title: 'Supercharging Amazon Agency Sales - Jeff | Equal Collective',
  description:
    'Jeff Sends 3x more emails, books 5x more meetings and costs 0.5x than your average sales representative, on autopilot!',
  image: '/images/og-image-v2.png',
  url: '/',
  icon: '/images/favicon.png',
};

export const metadata = {
  metadataBase: new URL('https://equalcollective.com'),
  title: metaDetails.title,
  description: metaDetails.description,
  icons: {
    icon: metaDetails.icon,
    shortcut: metaDetails.icon,
    apple: metaDetails.icon,
    other: {
      rel: 'apple-touch-icon-precomposed',
      url: metaDetails.icon,
    },
  },
  twitter: {
    card: 'summary_large_image',
    title: metaDetails.title,
    description: metaDetails.description,
    images: [metaDetails.image],
  },
  openGraph: {
    title: metaDetails.title,
    description: metaDetails.description,
    url: metaDetails.url,
    siteName: 'Equal Collective',
    images: [
      {
        url: metaDetails.image,
        width: 800,
        height: 600,
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  weight: ['400', '600', '500', '700', '800', '900'],
});

/* eslint-disable react/prop-types */
export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      className={inter.className}
      style={{ scrollBehavior: 'smooth' }}
    >
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#ffffff" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body>
        <Providers>
          <PostHogProvider>
            <Page>{children}</Page>
            <GoogleAnalytics />
            <Analytics />
          </PostHogProvider>
        </Providers>
      </body>
      {/*<GoogleTagManager gtmId="G-SB9V1VN3XP" />*/}
    </html>
  );
}
