/**
 * Test UUID5-Only PostHog Validation
 * 
 * This tests the updated PostHog validation that ONLY allows UUID5
 * and blocks all other UUID versions (1, 2, 3, 4, 6, 7, 8, etc.)
 */

// Import the validation function
const { isValidUUID5 } = require('./src/utils/posthog.js');
const { v4: uuidv4, v5: uuidv5, v7: uuidv7 } = require('uuid');

console.log('🔒 Testing UUID5-ONLY PostHog Validation\n');
console.log('=' .repeat(55));
console.log('📋 NEW RULE: Only UUID5 allowed, all others blocked');
console.log('=' .repeat(55));

// Test cases with various UUID versions
const testCases = [
  {
    name: 'UUID5 (Generated)',
    uuid: uuidv5('test-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: true,
    description: 'Should be ALLOWED (UUID5)'
  },
  {
    name: '<PERSON><PERSON>D5 (Another)',
    uuid: uuidv5('summer-2024', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: true,
    description: 'Should be ALLOWED (UUID5)'
  },
  {
    name: 'UUID4 (Random)',
    uuid: uuidv4(),
    expected: false,
    description: 'Should be BLOCKED (UUID4)'
  },
  {
    name: 'UUID7 (Time-based)',
    uuid: uuidv7(),
    expected: false,
    description: 'Should be BLOCKED (UUID7)'
  },
  {
    name: 'Your Original UUID7',
    uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0',
    expected: false,
    description: 'Should be BLOCKED (UUID7)'
  },
  {
    name: 'Simulated UUID8',
    uuid: '0198c3db-6e26-8894-aea5-3dc516a689c0',
    expected: false,
    description: 'Should be BLOCKED (UUID8)'
  },
  {
    name: 'Simulated UUID1',
    uuid: '550e8400-e29b-11d4-a716-************',
    expected: false,
    description: 'Should be BLOCKED (UUID1)'
  },
  {
    name: 'Simulated UUID3',
    uuid: '550e8400-e29b-31d4-a716-************',
    expected: false,
    description: 'Should be BLOCKED (UUID3)'
  },
  {
    name: 'Simulated UUID6',
    uuid: '550e8400-e29b-61d4-a716-************',
    expected: false,
    description: 'Should be BLOCKED (UUID6)'
  },
  {
    name: 'NIL UUID (Version 0)',
    uuid: '00000000-0000-0000-0000-000000000000',
    expected: false,
    description: 'Should be BLOCKED (UUID0/NIL)'
  },
  {
    name: 'MAX UUID (Version 15)',
    uuid: 'ffffffff-ffff-ffff-ffff-ffffffffffff',
    expected: false,
    description: 'Should be BLOCKED (UUID15/MAX)'
  },
  {
    name: 'Non-UUID String',
    uuid: 'campaign-123',
    expected: true,
    description: 'Should be ALLOWED (non-UUID)'
  },
  {
    name: 'Invalid UUID Format',
    uuid: 'not-a-uuid',
    expected: true,
    description: 'Should be ALLOWED (invalid format)'
  },
  {
    name: 'Empty String',
    uuid: '',
    expected: true,
    description: 'Should be ALLOWED (empty)'
  },
  {
    name: 'Null Value',
    uuid: null,
    expected: true,
    description: 'Should be ALLOWED (null)'
  }
];

console.log('\n📝 Running UUID5-Only Validation Tests:\n');

let passed = 0;
let failed = 0;
let uuid5Allowed = 0;
let otherUuidsBlocked = 0;

testCases.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}:`);
  console.log(`   UUID: ${test.uuid}`);
  console.log(`   Expected: ${test.expected ? 'ALLOWED' : 'BLOCKED'}`);
  console.log(`   Description: ${test.description}`);
  
  const result = isValidUUID5(test.uuid);
  const success = result === test.expected;
  
  console.log(`   Result: ${result ? 'ALLOWED' : 'BLOCKED'} ${success ? '✅' : '❌'}`);
  
  if (success) {
    passed++;
    if (test.description.includes('UUID5') && result) {
      uuid5Allowed++;
    } else if (test.description.includes('BLOCKED') && !result) {
      otherUuidsBlocked++;
    }
  } else {
    failed++;
    console.log(`   ⚠️  TEST FAILED: Expected ${test.expected}, got ${result}`);
  }
  
  console.log('');
});

// Summary
console.log('📊 Test Results Summary:\n');
console.log(`✅ Passed: ${passed}/${testCases.length}`);
console.log(`❌ Failed: ${failed}/${testCases.length}`);
console.log(`Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);
console.log(`\n🎯 UUID5 Specific Results:`);
console.log(`✅ UUID5 Allowed: ${uuid5Allowed}`);
console.log(`❌ Other UUIDs Blocked: ${otherUuidsBlocked}`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! UUID5-only validation is working correctly.');
  console.log('✅ Only UUID5 is allowed, all other UUID versions are blocked.');
} else {
  console.log('\n⚠️  Some tests failed. Please check the validation logic.');
}

// Test URL scenarios with different UUID versions
console.log('\n🌐 URL Scenario Testing (UUID5-Only Policy):\n');

const urlScenarios = [
  {
    name: 'UUID5 in campaign_id',
    uuid: uuidv5('valid-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: 'TRACKING ALLOWED'
  },
  {
    name: 'UUID4 in uuid parameter',
    uuid: uuidv4(),
    expected: 'TRACKING BLOCKED'
  },
  {
    name: 'UUID7 in utm_uuid',
    uuid: uuidv7(),
    expected: 'TRACKING BLOCKED'
  },
  {
    name: 'Your original UUID7',
    uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0',
    expected: 'TRACKING BLOCKED'
  },
  {
    name: 'UUID8 in n_uuid',
    uuid: '0198c3db-6e26-8894-aea5-3dc516a689c0',
    expected: 'TRACKING BLOCKED'
  },
  {
    name: 'Non-UUID in campaignId',
    uuid: 'summer-2024-promo',
    expected: 'TRACKING ALLOWED'
  }
];

urlScenarios.forEach((scenario, index) => {
  console.log(`${index + 1}. ${scenario.name}:`);
  console.log(`   UUID: ${scenario.uuid}`);
  console.log(`   URL: /audit/product?param=${scenario.uuid}`);
  
  const result = isValidUUID5(scenario.uuid);
  const trackingStatus = result ? 'TRACKING ALLOWED' : 'TRACKING BLOCKED';
  const success = trackingStatus === scenario.expected;
  
  console.log(`   Expected: ${scenario.expected}`);
  console.log(`   Result: ${trackingStatus} ${success ? '✅' : '❌'}`);
  console.log('');
});

// Generate some UUID5 examples for testing
console.log('\n🔧 Valid UUID5 Examples for Testing:\n');

const campaignNames = ['summer-2024', 'winter-sale', 'black-friday', 'spring-launch'];
const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';

campaignNames.forEach((campaign, index) => {
  const uuid5 = uuidv5(campaign, namespace);
  console.log(`${index + 1}. Campaign: "${campaign}"`);
  console.log(`   UUID5: ${uuid5}`);
  console.log(`   URL: /audit/product?campaign_id=${uuid5}`);
  console.log(`   Status: ✅ TRACKING ALLOWED\n`);
});

console.log('💡 Implementation Notes:\n');
console.log('• Only UUID5 (name-based, deterministic) is allowed');
console.log('• All other UUID versions (1, 2, 3, 4, 6, 7, 8) are blocked');
console.log('• Non-UUID strings are still allowed (campaign names, etc.)');
console.log('• Empty/null values are allowed');
console.log('• Uses uuid library for accurate version detection');
console.log('• Falls back to manual validation if library unavailable');

console.log('\n⚠️  Impact on Your System:\n');
console.log('• Your original UUID7 will now be BLOCKED');
console.log('• Any UUID4 (random) will be BLOCKED');
console.log('• Any UUID7 (time-based) will be BLOCKED');
console.log('• Any UUID8 (experimental) will be BLOCKED');
console.log('• Only UUID5 (name-based) will be ALLOWED');

console.log('\n' + '=' .repeat(55));
console.log('✨ UUID5-only validation testing complete!');
