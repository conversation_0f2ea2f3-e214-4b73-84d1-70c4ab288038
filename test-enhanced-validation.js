/**
 * Test Enhanced PostHog Validation
 * 
 * This tests the updated PostHog validation that uses the uuid library's
 * built-in validation functions for more robust UUID checking.
 */

// Import the enhanced validation function
const { isValidUUID5 } = require('./src/utils/posthog.js');
const { v4: uuidv4, v5: uuidv5, v7: uuidv7 } = require('uuid');

console.log('🧪 Testing Enhanced PostHog Validation\n');
console.log('=' .repeat(50));

// Test cases
const testCases = [
  {
    name: 'Your Original UUID7',
    uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0',
    expected: true,
    description: 'Should be allowed (UUID7)'
  },
  {
    name: 'Simulated UUID8',
    uuid: '0198c3db-6e26-8894-aea5-3dc516a689c0',
    expected: false,
    description: 'Should be blocked (UUID8)'
  },
  {
    name: 'Generated UUID4',
    uuid: uuidv4(),
    expected: true,
    description: 'Should be allowed (UUID4)'
  },
  {
    name: 'Generated UUID5',
    uuid: uuidv5('test-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: true,
    description: 'Should be allowed (UUID5)'
  },
  {
    name: 'Generated UUID7',
    uuid: uuidv7(),
    expected: true,
    description: 'Should be allowed (UUID7)'
  },
  {
    name: 'Non-UUID String',
    uuid: 'campaign-123',
    expected: true,
    description: 'Should be allowed (non-UUID)'
  },
  {
    name: 'Invalid UUID Format',
    uuid: 'not-a-uuid',
    expected: true,
    description: 'Should be allowed (invalid format)'
  },
  {
    name: 'NIL UUID',
    uuid: '00000000-0000-0000-0000-000000000000',
    expected: true,
    description: 'Should be allowed (NIL UUID, version 0)'
  },
  {
    name: 'Empty String',
    uuid: '',
    expected: true,
    description: 'Should be allowed (empty)'
  },
  {
    name: 'Null Value',
    uuid: null,
    expected: true,
    description: 'Should be allowed (null)'
  }
];

console.log('\n📝 Running Validation Tests:\n');

let passed = 0;
let failed = 0;

testCases.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}:`);
  console.log(`   UUID: ${test.uuid}`);
  console.log(`   Expected: ${test.expected ? 'ALLOWED' : 'BLOCKED'}`);
  console.log(`   Description: ${test.description}`);
  
  const result = isValidUUID5(test.uuid);
  const success = result === test.expected;
  
  console.log(`   Result: ${result ? 'ALLOWED' : 'BLOCKED'} ${success ? '✅' : '❌'}`);
  
  if (success) {
    passed++;
  } else {
    failed++;
    console.log(`   ⚠️  TEST FAILED: Expected ${test.expected}, got ${result}`);
  }
  
  console.log('');
});

// Summary
console.log('📊 Test Results Summary:\n');
console.log(`✅ Passed: ${passed}/${testCases.length}`);
console.log(`❌ Failed: ${failed}/${testCases.length}`);
console.log(`Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! Enhanced validation is working correctly.');
} else {
  console.log('\n⚠️  Some tests failed. Please check the validation logic.');
}

// Test URL scenarios
console.log('\n🌐 URL Scenario Testing:\n');

const urlScenarios = [
  {
    name: 'Valid UUID7 in campaign_id',
    url: '/audit/product?campaign_id=0198c3db-6e26-7894-aea5-3dc516a689c0',
    uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0',
    expected: 'TRACKING ALLOWED'
  },
  {
    name: 'UUID8 in utm_uuid',
    url: '/audit/product?utm_uuid=0198c3db-6e26-8894-aea5-3dc516a689c0',
    uuid: '0198c3db-6e26-8894-aea5-3dc516a689c0',
    expected: 'TRACKING BLOCKED'
  },
  {
    name: 'UUID5 in uuid parameter',
    url: `/audit/product?uuid=${uuidv5('summer-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341')}`,
    uuid: uuidv5('summer-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: 'TRACKING ALLOWED'
  },
  {
    name: 'Non-UUID in n_uuid',
    url: '/audit/product?n_uuid=campaign-2024',
    uuid: 'campaign-2024',
    expected: 'TRACKING ALLOWED'
  }
];

urlScenarios.forEach((scenario, index) => {
  console.log(`${index + 1}. ${scenario.name}:`);
  console.log(`   URL: ${scenario.url}`);
  console.log(`   UUID: ${scenario.uuid}`);
  
  const result = isValidUUID5(scenario.uuid);
  const trackingStatus = result ? 'TRACKING ALLOWED' : 'TRACKING BLOCKED';
  const success = trackingStatus === scenario.expected;
  
  console.log(`   Expected: ${scenario.expected}`);
  console.log(`   Result: ${trackingStatus} ${success ? '✅' : '❌'}`);
  console.log('');
});

// Performance comparison (basic)
console.log('\n⚡ Performance Note:\n');
console.log('Enhanced validation benefits:');
console.log('• Uses uuid library\'s optimized validation');
console.log('• More accurate than manual regex parsing');
console.log('• Handles edge cases (NIL, MAX UUIDs)');
console.log('• Future-proof for new UUID versions');
console.log('• Maintained by the UUID library team');

console.log('\n💡 Integration Notes:\n');
console.log('• The enhanced validation is backward compatible');
console.log('• Falls back to manual validation if uuid library unavailable');
console.log('• No changes needed to existing tracking calls');
console.log('• Provides better error handling and logging');

console.log('\n' + '=' .repeat(50));
console.log('✨ Enhanced validation testing complete!');
