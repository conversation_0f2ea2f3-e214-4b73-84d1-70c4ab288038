'use client';

import React, { useEffect, useState } from 'react';

import {
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Button,
  Box,
  Tabs,
  Tab,
  Container,
  Snackbar,
  Alert,
  AppBar,
  Toolbar,
  Typography,
  Card,
  CardContent,
  IconButton,
  Tooltip,
} from '@mui/material';

import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';

import { useClients } from './Utils/getClients';
import { useCampaigns } from './Utils/campaigns';

import BatchJobDashboard from './BatchJobDashboard';
import getJobs from './Utils/getJobs';
import SingleJobDashboard from './SingleJobDashboard';
import { useAdminTheme } from './AdminThemeContext';
import { useUser } from './Utils/getUser';

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

const JeffDashboard = () => {
  const { data: user } = useUser();
  const { data: clients } = useClients();
  const [imitatedClient, setImitatedClient] = useState({
    name: user?.name || '',
    id: user?.id || '1',
  });
  const [selectedIds, setSelectedIds] = useState([]);
  const [value, setValue] = useState(0);
  const [snackBar, setSnackBar] = useState({
    isOpen: false,
    message: '',
    severity: 'success',
  });
  const router = useRouter();
  const isAdmin = user?.userType === 'admin';
  const { mode: adminThemeMode, toggleTheme: toggleAdminTheme } = useAdminTheme() || { mode: 'light', toggleTheme: () => {} };
  const isDarkMode = adminThemeMode === 'dark';

  const { data: jobs, isLoading: isJobsLoading } = useQuery({
    queryKey: ['jeff-jobs', { clientId: imitatedClient.id }],
    queryFn: () => getJobs(imitatedClient.id),
    enabled: !!imitatedClient.id,
  });

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const { data: campaigns } = useCampaigns();

  useEffect(() => {
    if (user) {
      setImitatedClient({ name: user.name, id: user.id });
      setSelectedIds([]);
    }
  }, [user]);

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      minHeight: '100vh',
      bgcolor: adminThemeMode === 'dark' ? '#222B45' : '#ffffff'
    }}>
      {/* Top AppBar */}
      <AppBar 
        position="static" 
        elevation={1}
        sx={{ 
          backgroundColor: isDarkMode ? '#222B45' : '#ffffff',
          color: isDarkMode ? '#ffffff' : '#1e2022',
          borderBottom: isDarkMode ? 'none' : '1px solid rgba(0, 0, 0, 0.08)'
        }}
      >
        <Toolbar sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DashboardIcon sx={{ 
              mr: 1.5,
              color: isDarkMode ? '#ffffff' : '#2f6ad9'
            }} />
            <Typography variant="h6" noWrap component="div" sx={{ 
              color: isDarkMode ? '#ffffff' : '#000000', 
              fontWeight: 500 
            }}>
              Dashboard
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {isAdmin && (
              <Button
                variant="contained"
                color="primary"
                size="small"
                startIcon={<PeopleIcon />}
                onClick={() => router.push('/jeff/admin/client')}
                sx={{ 
                  boxShadow: isDarkMode ? '0px 2px 4px rgba(0,0,0,0.3)' : '0px 1px 2px rgba(0,0,0,0.1)',
                  backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                  '&:hover': {
                    boxShadow: isDarkMode ? '0px 3px 6px rgba(0,0,0,0.4)' : '0px 2px 4px rgba(0,0,0,0.2)',
                    backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8'
                  },
                  transition: 'all 0.2s ease-in-out',
                  fontWeight: 500,
                  textTransform: 'none',
                  padding: '6px 16px',
                  borderRadius: '4px'
                }}
              >
                Admin Panel
              </Button>
            )}

            <Tooltip title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
              <IconButton 
                sx={{ 
                  bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.04)',
                  '&:hover': {
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.08)',
                  },
                  borderRadius: 1,
                  p: 1,
                  transition: 'background-color 0.2s ease-in-out',
                  color: isDarkMode ? '#ffffff' : '#333333',
                }} 
                onClick={toggleAdminTheme} 
                color="inherit"
                aria-label="toggle dark mode"
              >
                {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
              </IconButton>
            </Tooltip>

            {isAdmin && clients && clients.length > 0 && (
              <FormControl 
                sx={{ 
                  minWidth: 220, 
                  maxWidth: 300,
                  backgroundColor: adminThemeMode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.95)',
                  borderRadius: 1,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    color: adminThemeMode === 'dark' ? '#ffffff' : '#333333',
                    borderColor: adminThemeMode === 'dark' ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)'
                  },
                  '& .MuiInputLabel-root': {
                    color: adminThemeMode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                  },
                  '& .MuiSelect-icon': {
                    color: adminThemeMode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.54)'
                  }
                }} 
                size="small"
              >
                <InputLabel id="client-select-label">Select Client</InputLabel>
                <Select
                  labelId="client-select-label"
                  id="client-select"
                  name="client"
                  label="Select Client"
                  value={imitatedClient.name}
                  size="small"
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        bgcolor: adminThemeMode === 'dark' ? '#2D3748' : '#ffffff',
                        color: adminThemeMode === 'dark' ? '#ffffff' : '#000000',
                      }
                    }
                  }}
                >
                  {clients.map((client, idx) => (
                    <MenuItem
                      key={idx}
                      onClick={() => {
                        setImitatedClient({ ...client });
                        setSelectedIds([]);
                      }}
                      value={client.name}
                      sx={{
                        '&.Mui-selected': {
                          backgroundColor: adminThemeMode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.04)'
                        },
                        '&:hover': {
                          backgroundColor: adminThemeMode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      {client.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4, flexGrow: 1 }}>
        <Card 
          elevation={isDarkMode ? 2 : 1}
          sx={{
            backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
            color: isDarkMode ? '#ffffff' : '#000000',
            border: isDarkMode ? 'none' : '1px solid rgba(0, 0, 0, 0.08)',
            boxShadow: isDarkMode 
              ? '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)'
              : '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.07),0px 1px 3px 0px rgba(0,0,0,0.06)'
          }}
        >
          <CardContent>
            <Box sx={{ 
              borderBottom: 1, 
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)', 
              mb: 2 
            }}>
              <Tabs
                value={value}
                onChange={handleChange}
                aria-label="dashboard tabs"
                indicatorColor="primary"
                textColor="primary"
                sx={{
                  '& .MuiTab-root': {
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                    fontWeight: 500,
                    transition: 'color 0.2s ease-in-out',
                    '&.Mui-selected': {
                      color: isDarkMode ? '#ffffff' : '#2f6ad9',
                      fontWeight: 600,
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: isDarkMode ? '#4299E1' : '#2f6ad9',
                    height: 3,
                    borderTopLeftRadius: 3,
                    borderTopRightRadius: 3,
                  }
                }}
              >
                <Tab
                  label="Batch Job"
                  sx={{ fontWeight: 600, fontSize: { xs: '0.9rem', sm: '1rem' } }}
                  {...a11yProps(0)}
                />
                {user?.singleEntryOpt && (
                  <Tab
                    label="Single Job"
                    sx={{ fontWeight: 600, fontSize: { xs: '0.9rem', sm: '1rem' } }}
                    {...a11yProps(1)}
                  />
                )}
              </Tabs>
            </Box>

            <CustomTabPanel value={value} index={0}>
              <BatchJobDashboard
                user={user}
                imitatedClient={imitatedClient}
                setImitatedClient={setImitatedClient}
                selectedIds={selectedIds}
                setSelectedIds={setSelectedIds}
                jobs={jobs?.jobs || []}
                campaigns={campaigns?.campaigns || []}
                isLoading={isJobsLoading}
              />
            </CustomTabPanel>

            {user?.singleEntryOpt && (
              <CustomTabPanel value={value} index={1}>
                <SingleJobDashboard
                  user={user}
                  imitatedClient={imitatedClient}
                  setImitatedClient={setImitatedClient}
                  jobs={jobs?.singleJobs || []}
                  setSnackBar={setSnackBar}
                  campaigns={campaigns?.campaigns || []}
                  isLoading={isJobsLoading}
                />
              </CustomTabPanel>
            )}
          </CardContent>
        </Card>
      </Container>

      {snackBar.isOpen && (
        <Snackbar
          open={snackBar.isOpen}
          autoHideDuration={6000}
          onClose={() => setSnackBar({ ...snackBar, isOpen: false })}
        >
          <Alert
            onClose={() => setSnackBar({ ...snackBar, isOpen: false })}
            severity={snackBar.severity}
          >
            {snackBar.message}
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
};

export default JeffDashboard;

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{
        backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
        borderRadius: '4px',
        transition: 'background-color 0.2s ease-in-out',
      }}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}
