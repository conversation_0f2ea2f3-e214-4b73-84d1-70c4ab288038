# UUID Testing Setup Guide

## Quick Start

1. **Install the uuid package:**
   ```bash
   npm install uuid
   ```

2. **Run the UUID generator test:**
   ```bash
   node uuid-library-examples.js
   ```

3. **Run the Jest tests:**
   ```bash
   npm test src/utils/__tests__/posthog-uuid-validation.test.js
   ```

## Using the UUID Library

### Basic UUID5 Generation (Recommended)

```javascript
import { v5 as uuidv5 } from 'uuid';

// Define a namespace UUID (use the same one for consistency)
const MY_NAMESPACE_UUID = '1b671a64-40d5-491e-99b0-da01ff1f3341';

// Generate UUID5 (deterministic - same input = same output)
const campaignUuid = uuidv5('summer-2024-campaign', MY_NAMESPACE_UUID);
console.log(campaignUuid); // Always: 7c9e6679-7425-40de-944b-e07fc1f90ae7

// Test with PostHog validation
import { isValidUUID5 } from './src/utils/posthog';
console.log(isValidUUID5(campaignUuid)); // true (tracking allowed)
```

### Basic UUID4 Generation

```javascript
import { v4 as uuidv4 } from 'uuid';

// Generate UUID4 (random - different every time)
const randomUuid = uuidv4();
console.log(randomUuid); // e.g., 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d

// Test with PostHog validation
console.log(isValidUUID5(randomUuid)); // true (tracking allowed)
```

### Simulating UUID8 for Testing

```javascript
import crypto from 'crypto';

// UUID8 is not standardized yet, so we simulate it for testing
const generateUUID8 = (customData) => {
  const hash = crypto.createHash('sha256');
  hash.update(customData, 'utf8');
  const hashBytes = hash.digest();
  
  // Set version (8) and variant bits
  hashBytes[6] = (hashBytes[6] & 0x0f) | 0x80; // Version 8
  hashBytes[8] = (hashBytes[8] & 0x3f) | 0x80; // Variant 10
  
  // Format as UUID string
  const hex = hashBytes.toString('hex');
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    hex.substring(12, 16),
    hex.substring(16, 20),
    hex.substring(20, 32)
  ].join('-');
};

const uuid8 = generateUUID8('test-campaign');
console.log(uuid8); // e.g., 550e8400-e29b-81d4-a716-************

// Test with PostHog validation
console.log(isValidUUID5(uuid8)); // false (tracking blocked)
```

## Test Examples

### Example 1: Valid UUID5 Campaign
```javascript
import { v5 as uuidv5 } from 'uuid';

const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';
const campaignUuid = uuidv5('black-friday-2024', namespace);

// URL: /audit/product?campaign_id=7c9e6679-7425-40de-944b-e07fc1f90ae7
// Result: ✅ Tracking allowed (UUID5)
```

### Example 2: Blocked UUID8 Campaign
```javascript
const uuid8 = generateUUID8('black-friday-2024');

// URL: /audit/product?campaign_id=a1b2c3d4-e5f6-8789-abcd-ef0123456789
// Result: ❌ Tracking blocked (UUID8)
```

### Example 3: Test Suite
```javascript
describe('PostHog UUID Validation', () => {
  test('should allow UUID5', () => {
    const uuid5 = uuidv5('test-campaign', MY_NAMESPACE);
    expect(isValidUUID5(uuid5)).toBe(true);
  });

  test('should block UUID8', () => {
    const uuid8 = generateUUID8('test-campaign');
    expect(isValidUUID5(uuid8)).toBe(false);
  });
});
```

## Generated Test Data

After running `node uuid-library-examples.js`, you'll get:

### UUID5 Examples (Allowed)
```
Campaign: "summer-2024-campaign"
UUID5: 7c9e6679-7425-40de-944b-e07fc1f90ae7 (version: 5)

Campaign: "winter-holiday-promo"  
UUID5: 8f4e8c2d-1a2b-5c3d-8e4f-9a0b1c2d3e4f (version: 5)
```

### UUID8 Examples (Blocked)
```
Campaign: "summer-2024-campaign"
UUID8: a1b2c3d4-e5f6-8789-abcd-ef0123456789 (version: 8)

Campaign: "winter-holiday-promo"
UUID8: b2c3d4e5-f6a7-8b9c-def0-123456789abc (version: 8)
```

## URL Testing Scenarios

### ✅ Allowed URLs
```
/audit/product?uuid=7c9e6679-7425-40de-944b-e07fc1f90ae7          (UUID5)
/audit/product?campaign_id=9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d  (UUID4)
/audit/product?utm_uuid=8f4e8c2d-1a2b-5c3d-8e4f-9a0b1c2d3e4f    (UUID5)
/audit/product?n_uuid=campaign-123                               (Non-UUID)
```

### ❌ Blocked URLs
```
/audit/product?uuid=a1b2c3d4-e5f6-8789-abcd-ef0123456789         (UUID8)
/audit/product?campaign_id=b2c3d4e5-f6a7-8b9c-def0-123456789abc (UUID8)
/audit/product?utm_uuid=c3d4e5f6-a7b8-8c9d-ef01-23456789abcd    (UUID8)
```

## Console Output Examples

### When UUID8 is detected:
```
UUID8 detected - skipping tracking: a1b2c3d4-e5f6-8789-abcd-ef0123456789
Tracking blocked due to UUID8 in parameter 'campaign_id': a1b2c3d4-e5f6-8789-abcd-ef0123456789
Audit page view tracking skipped due to UUID8 detection
```

### When UUID5 is detected:
```
Valid UUID5 detected: 7c9e6679-7425-40de-944b-e07fc1f90ae7
Audit page view tracked successfully - Client: Test Client
```

## Integration with Your Tests

1. **Install uuid package**: `npm install uuid`
2. **Import in your tests**: `import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';`
3. **Use consistent namespace**: `const NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';`
4. **Generate test UUIDs**: `const uuid5 = uuidv5('campaign-name', NAMESPACE);`
5. **Test validation**: `expect(isValidUUID5(uuid5)).toBe(true);`

## Why UUID5 vs UUID8?

- **UUID5**: Deterministic, based on SHA-1 hash of name + namespace
  - ✅ **Allowed** by your PostHog validation
  - Same input always produces same UUID
  - Perfect for consistent campaign IDs

- **UUID8**: Custom/experimental format (not standardized)
  - ❌ **Blocked** by your PostHog validation  
  - Used to test the blocking functionality
  - Simulated for testing purposes

The validation ensures that when UUID8 appears in your URLs (likely from external systems or tests), PostHog tracking is automatically disabled to prevent unwanted data collection.
