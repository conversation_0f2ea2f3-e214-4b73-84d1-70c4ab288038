'use client';
import React from 'react';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';

const enableTrackingOnLocalhost = true;

if (
  typeof window !== 'undefined' &&
  !window.location.search.includes('?source=pdf') &&
  (enableTrackingOnLocalhost || !window.location.host.includes('localhost'))
) {
  const PUBLIC_POSTHOG_KEY = process.env.NEXT_PUBLIC_POSTHOG_KEY;
  const PUBLIC_POSTHOG_HOST = process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com';

  // Only initialize if we have an API key
  if (PUBLIC_POSTHOG_KEY) {
    console.log('Initializing global PostHog tracking...');
    posthog.init(PUBLIC_POSTHOG_KEY, {
      api_host: PUBLIC_POSTHOG_HOST,
      person_profiles: 'identified_only',
      enable_heatmaps: true,
      capture_pageview: true,
      capture_pageleave: true,
    });
  } else {
    console.warn('PostHog API key not found - global tracking disabled');
  }
}

export function CSPostHogProvider({ children }) {
  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
