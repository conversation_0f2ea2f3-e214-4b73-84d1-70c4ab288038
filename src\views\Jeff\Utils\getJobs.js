import { API_ENDPOINTS, getJeffBaseURL } from '../../../config/api';
import Cookies from 'js-cookie';
import axios from 'axios';

const isServer = typeof window === 'undefined';

const cookiesKey = 'jeff-authorization';
const baseUrl = getJeffBaseURL();

const getJobs = async (id) => {
  let authorization;
  if (isServer) {
    const { cookies } = await import('next/headers');
    const cookieStore = cookies();
    authorization = cookieStore.get(cookiesKey).value;
  } else {
    authorization = Cookies.get(cookiesKey);
  }

  const url = baseUrl + API_ENDPOINTS.JOBS + `/${id}`;
  const res = await axios.get(url, {
    headers: {
      Authorization: authorization,
    },
  });

  const data = res.data;

  return data;
};

export default getJobs;
