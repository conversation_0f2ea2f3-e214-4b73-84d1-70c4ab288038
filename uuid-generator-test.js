/**
 * Standalone UUID Generator and Validator Test
 *
 * This file generates UUID5 and UUID8 for testing the PostHog validation
 * Run with: npm install uuid && node uuid-generator-test.js
 */

const crypto = require('crypto');
const { v4: uuidv4, v5: uuidv5 } = require('uuid');

// Test namespace UUID
const MY_NAMESPACE_UUID = '1b671a64-40d5-491e-99b0-da01ff1f3341';

// UUID Generation Functions using the uuid library
const generateUUID4 = () => {
  return uuidv4();
};

const generateUUID5 = (name, namespace = MY_NAMESPACE_UUID) => {
  return uuidv5(name, namespace);
};

const generateUUID8 = (customData = 'test-data-for-uuid8') => {
  const hash = crypto.createHash('sha256');
  hash.update(customData, 'utf8');
  const hashBytes = hash.digest();
  
  // Set version (8) and variant bits
  hashBytes[6] = (hashBytes[6] & 0x0f) | 0x80; // Version 8
  hashBytes[8] = (hashBytes[8] & 0x3f) | 0x80; // Variant 10
  
  // Format as UUID string
  const hex = hashBytes.toString('hex');
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    hex.substring(12, 16),
    hex.substring(16, 20),
    hex.substring(20, 32)
  ].join('-');
};

// Validation function (simplified version of the one in posthog.js)
const isValidUUID5 = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true;
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(uuid)) {
    return true; // Allow non-UUID strings
  }
  
  const version = parseInt(uuid.charAt(14), 16);
  
  if (version === 8) {
    console.log(`❌ UUID8 detected - would block tracking: ${uuid}`);
    return false;
  }
  
  if (version === 5) {
    console.log(`✅ UUID5 detected - would allow tracking: ${uuid}`);
    return true;
  }
  
  console.log(`✅ UUID version ${version} detected - would allow tracking: ${uuid}`);
  return true;
};

// Test runner
const runTests = () => {
  console.log('🧪 UUID Generation and Validation Test\n');
  console.log('=' .repeat(60));
  
  // Generate test UUIDs
  console.log('\n📝 Generating Test UUIDs:\n');
  
  const testCampaigns = [
    'summer-2024-campaign',
    'winter-holiday-promo',
    'black-friday-sale',
    'spring-launch',
    'customer-retention'
  ];
  
  const uuid5s = [];
  const uuid8s = [];
  
  console.log('🟢 UUID5 Generation (ALLOWED):');
  testCampaigns.forEach((campaign, index) => {
    const uuid5 = generateUUID5(campaign);
    uuid5s.push({ campaign, uuid: uuid5 });
    console.log(`  ${index + 1}. ${campaign}`);
    console.log(`     UUID5: ${uuid5}`);
    console.log(`     Version: ${uuid5.charAt(14)} (at position 14)`);
  });
  
  console.log('\n🔴 UUID8 Generation (BLOCKED):');
  testCampaigns.forEach((campaign, index) => {
    const uuid8 = generateUUID8(campaign);
    uuid8s.push({ campaign, uuid: uuid8 });
    console.log(`  ${index + 1}. ${campaign}`);
    console.log(`     UUID8: ${uuid8}`);
    console.log(`     Version: ${uuid8.charAt(14)} (at position 14)`);
  });
  
  // Test validation
  console.log('\n' + '=' .repeat(60));
  console.log('\n🔍 Validation Test Results:\n');
  
  console.log('Testing UUID5s (should all be ALLOWED):');
  uuid5s.forEach(({ campaign, uuid }) => {
    const result = isValidUUID5(uuid);
    console.log(`  ${campaign}: ${result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
  });
  
  console.log('\nTesting UUID8s (should all be BLOCKED):');
  uuid8s.forEach(({ campaign, uuid }) => {
    const result = isValidUUID5(uuid);
    console.log(`  ${campaign}: ${result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
  });
  
  // Test UUID4 for comparison
  console.log('\nTesting UUID4 (should be ALLOWED):');
  const uuid4 = generateUUID4();
  console.log(`  Generated UUID4: ${uuid4}`);
  console.log(`  Version: ${uuid4.charAt(14)}`);
  const uuid4Result = isValidUUID5(uuid4);
  console.log(`  Result: ${uuid4Result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
  
  // Test edge cases
  console.log('\n' + '=' .repeat(60));
  console.log('\n🧩 Edge Case Testing:\n');
  
  const edgeCases = [
    { name: 'Empty string', value: '' },
    { name: 'Null', value: null },
    { name: 'Undefined', value: undefined },
    { name: 'Non-UUID string', value: 'campaign-123' },
    { name: 'Invalid UUID format', value: 'not-a-uuid-at-all' },
    { name: 'Partial UUID', value: '550e8400-e29b-51d4' }
  ];
  
  edgeCases.forEach(({ name, value }) => {
    const result = isValidUUID5(value);
    console.log(`  ${name}: ${result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
  });
  
  // URL simulation test
  console.log('\n' + '=' .repeat(60));
  console.log('\n🌐 URL Parameter Simulation:\n');
  
  const urlTests = [
    { name: 'Valid UUID5 in URL', uuid: uuid5s[0].uuid, param: 'uuid' },
    { name: 'UUID8 in URL (blocked)', uuid: uuid8s[0].uuid, param: 'uuid' },
    { name: 'UUID5 in campaign_id', uuid: uuid5s[1].uuid, param: 'campaign_id' },
    { name: 'UUID8 in utm_uuid (blocked)', uuid: uuid8s[1].uuid, param: 'utm_uuid' },
    { name: 'Non-UUID in campaign_id', uuid: 'summer-2024', param: 'campaign_id' }
  ];
  
  urlTests.forEach(({ name, uuid, param }) => {
    const url = `https://example.com/audit/product?${param}=${uuid}`;
    const result = isValidUUID5(uuid);
    console.log(`  ${name}:`);
    console.log(`    URL: ${url}`);
    console.log(`    Tracking: ${result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
    console.log('');
  });
  
  console.log('=' .repeat(60));
  console.log('\n✨ Test Complete! Use these UUIDs in your test cases.\n');
  
  // Export for use in tests
  return {
    uuid5s: uuid5s.map(item => item.uuid),
    uuid8s: uuid8s.map(item => item.uuid),
    uuid4: uuid4,
    generators: {
      generateUUID5,
      generateUUID8,
      generateUUID4
    }
  };
};

// Run tests if this file is executed directly
if (require.main === module) {
  const results = runTests();
  
  // Save results to a JSON file for use in other tests
  const fs = require('fs');
  fs.writeFileSync('generated-uuids.json', JSON.stringify(results, null, 2));
  console.log('📁 Results saved to generated-uuids.json');
}

module.exports = {
  generateUUID5,
  generateUUID8,
  generateUUID4,
  isValidUUID5,
  runTests
};
