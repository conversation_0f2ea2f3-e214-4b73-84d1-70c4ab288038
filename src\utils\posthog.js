'use client';

import posthog from 'posthog-js';

// Initialize PostHog - should be called once
let isInitialized = false;

/**
 * Helper function to safely stringify complex objects for PostHog properties
 * 
 * PostHog properties should be primitive values (string, number, boolean, null).
 * When objects or arrays are sent as properties without stringification, 
 * they appear as "[object Object]" in the PostHog dashboard.
 * 
 * This function:
 * - Keeps primitive values (string, number, boolean, null, undefined) as-is
 * - Converts objects and arrays to JSON strings
 * - Handles nested structures properly
 * 
 * @param {Object} obj - The properties object to process
 * @returns {Object} - Object with complex properties stringified
 * 
 * @example
 * // Before: { user: { id: 1, name: "<PERSON>" }, tags: ["a", "b"] }
 * // After:  { user: '{"id":1,"name":"John"}', tags: '["a","b"]' }
 */
export const stringifyComplexProperties = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (value === null || value === undefined) {
      result[key] = value;
    } else if (typeof value === 'object' && !Array.isArray(value)) {
      // Stringify nested objects
      result[key] = JSON.stringify(value);
    } else if (Array.isArray(value)) {
      // Stringify arrays
      result[key] = JSON.stringify(value);
    } else {
      // Keep primitive values as-is (string, number, boolean)
      result[key] = value;
    }
  }
  
  return result;
};

export const initPostHog = () => {
  if (typeof window === 'undefined') {
    console.log('PostHog init skipped: server-side rendering');
    return;
  }
  
  if (isInitialized) {
    console.log('PostHog already initialized, skipping...');
    return;
  }
  
  const apiKey = process.env.NEXT_PUBLIC_POSTHOG_KEY || 'phc_test_key';
  const host = process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com';
  
  console.log('Initializing PostHog with:', { 
    apiKey: apiKey ? apiKey.substring(0, 10) + '...' : 'NO_KEY', 
    host,
    hasRealKey: !!process.env.NEXT_PUBLIC_POSTHOG_KEY
  });
  
  posthog.init(apiKey, {
    api_host: host,
    capture_pageview: true, // Enable automatic pageview tracking
    capture_pageleave: true,
    autocapture: true, // Enable automatic click tracking
    persistence: 'localStorage+cookie',
    disable_session_recording: false,
    session_recording: {
      maskAllInputs: false,
      maskInputOptions: {
        password: true,
        email: false,
      },
    },
    // Enable debug mode for development
    debug: process.env.NODE_ENV === 'development',
    // Reduce sampling in development
    sampling: process.env.NODE_ENV === 'development' ? 1 : 0.1,
  });
  
  isInitialized = true;
  console.log('PostHog initialized successfully');
};

/**
 * Check if the current visitor is likely a bot
 */
export const isBot = () => {
  if (typeof window === 'undefined') return false;

  const botPatterns = [
    'bot', 'spider', 'crawl', 'slurp', 'lighthouse', 'headless',
    'prerender', 'screenshot', 'scrape', 'phantom', 'puppeteer',
    'selenium', 'webdriver', 'cypress', 'playwright'
  ];

  const userAgent = navigator.userAgent.toLowerCase();
  return botPatterns.some(pattern => userAgent.includes(pattern));
};

/**
 * Validate UUID and check if it's the expected version (UUID5)
 * Returns true ONLY for UUID5, blocks all other UUID versions
 * Uses the uuid library's built-in validation for better accuracy
 * @param {string} uuid - The UUID string to validate
 * @returns {boolean} - True if valid UUID5, false for all other UUID versions
 */
export const isValidUUID5 = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true; // Allow empty/missing UUIDs

  // Try to import uuid library functions
  let uuidValidate, uuidVersion;
  try {
    const uuidLib = require('uuid');
    uuidValidate = uuidLib.validate;
    uuidVersion = uuidLib.version;
  } catch (error) {
    console.warn('UUID library not available, falling back to manual validation');
    // Fallback to manual validation if uuid library not available
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

    if (!uuidRegex.test(uuid)) {
      return true; // Allow non-UUID strings to pass through
    }

    const version = parseInt(uuid.charAt(14), 16);

    if (version === 5) {
      console.log('Valid UUID5 detected - allowing tracking:', uuid);
      return true; // Allow only UUID5
    }

    console.log(`UUID version ${version} detected - blocking tracking (only UUID5 allowed):`, uuid);
    return false; // Block all non-UUID5 versions
  }

  // Use uuid library's built-in validation (preferred method)
  if (!uuidValidate(uuid)) {
    return true; // Allow non-UUID strings to pass through
  }

  try {
    const version = uuidVersion(uuid);

    if (version === 5) {
      console.log('Valid UUID5 detected - allowing tracking:', uuid);
      return true; // Allow only UUID5
    }

    // Block all other UUID versions (0, 1, 2, 3, 4, 6, 7, 8, 15)
    console.log(`UUID version ${version} detected - blocking tracking (only UUID5 allowed):`, uuid);
    return false;
  } catch (error) {
    console.error('Error checking UUID version:', error);
    return true; // Allow on error to prevent breaking functionality
  }
};

/**
 * Check if tracking should be allowed based on searchParams UUIDs
 * @param {URLSearchParams|Object} searchParams - Search parameters to check
 * @returns {boolean} - True if tracking should proceed, false if UUID8 detected
 */
export const shouldAllowTracking = (searchParams) => {
  if (typeof window === 'undefined') return true;

  try {
    // Get current URL search params if not provided
    const params = searchParams || new URLSearchParams(window.location.search);

    // Check common UUID parameter names
    const uuidParams = ['uuid', 'n_uuid', 'utm_uuid', 'campaign_id', 'campaignId'];

    for (const paramName of uuidParams) {
      let paramValue;

      if (params instanceof URLSearchParams) {
        paramValue = params.get(paramName);
      } else if (typeof params === 'object') {
        paramValue = params[paramName];
      }

      if (paramValue && !isValidUUID5(paramValue)) {
        console.log(`Tracking blocked due to UUID8 in parameter '${paramName}':`, paramValue);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error checking UUID validation:', error);
    return true; // Allow tracking on error to avoid breaking functionality
  }
};

/**
 * Validate and clean tracking data to ensure no empty values
 */
const validateTrackingData = (data) => {
  const cleanValue = (value, fallback = 'not_provided') => {
    if (!value || value === 'unknown' || value === '' || value === null || value === undefined) {
      return fallback;
    }
    return String(value).trim();
  };

  return {
    campaign_id: cleanValue(data.campaignId, 'no_campaign'),
    is_tracking: Boolean(data.isTracking),
    has_user_data: Boolean(data.hasUserData),
    company_name: cleanValue(data.companyName, 'no_company'),
    audit_id: cleanValue(data.auditId, 'no_audit_id'),
    audit_slug: cleanValue(data.auditSlug, 'no_slug'),
    client_name: cleanValue(data.clientName, 'no_client'),
    user_email: cleanValue(data.userEmail, 'no_email'),
    seller_id: cleanValue(data.sellerId, 'no_seller_id'),
    url_path: window.location.pathname,
    referrer: document.referrer || 'direct',
    user_agent: navigator.userAgent,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Track audit page view with enhanced naming and validation
 * Event: "audit_page_viewed"
 */
export const trackAuditPageView = (data) => {
  if (typeof window === 'undefined' || isBot()) {
    return;
  }

  // Check UUID validation before proceeding with tracking
  if (!shouldAllowTracking()) {
    console.log('Audit page view tracking skipped due to UUID8 detection');
    return;
  }

  if (!isInitialized) {
    initPostHog();
  }

  try {
    console.log('Tracking audit page view with data:', data);

    // Validate and clean data
    const cleanData = validateTrackingData(data);

    // Additional UUID validation on campaign_id if present
    if (cleanData.campaign_id && cleanData.campaign_id !== 'no_campaign') {
      if (!isValidUUID5(cleanData.campaign_id)) {
        console.log('Audit page view tracking skipped due to UUID8 in campaign_id:', cleanData.campaign_id);
        return;
      }
    }

    // Identify user if we have email
    if (data.userEmail && data.userEmail !== 'no_email') {
      posthog.identify(data.userEmail, {
        email: cleanData.user_email,
        seller_id: cleanData.seller_id,
        client_name: cleanData.client_name,
      });
    }

    // Set person properties with clean data
    posthog.people.set({
      user_email: cleanData.user_email,
      seller_id: cleanData.seller_id,
      campaign_id: cleanData.campaign_id,
      company_name: cleanData.company_name,
      audit_slug: cleanData.audit_slug,
      client_name: cleanData.client_name,
      last_audit_viewed: cleanData.timestamp,
    });

    // Create comprehensive event data
    const eventData = stringifyComplexProperties({
      ...cleanData,
      event_type: 'page_view',
      page_type: 'audit',
      // Add original data as stringified JSON for debugging
      original_data: JSON.stringify(data),
    });

    // Track with descriptive event name
    posthog.capture('audit_page_viewed', eventData);
    console.log('Audit page view tracked successfully - Client:', cleanData.client_name);

  } catch (error) {
    console.error('Failed to track audit page view:', error);
  }
};

/**
 * Track specific audit interactions with descriptive event names
 */
export const trackAuditInteraction = (data) => {
  if (typeof window === 'undefined' || isBot()) {
    return;
  }

  // Check UUID validation before proceeding with tracking
  if (!shouldAllowTracking()) {
    console.log('Audit interaction tracking skipped due to UUID8 detection');
    return;
  }

  if (!isInitialized) {
    return;
  }

  try {
    // Validate and clean base data
    const cleanData = validateTrackingData(data);

    // Additional UUID validation on campaign_id if present
    if (cleanData.campaign_id && cleanData.campaign_id !== 'no_campaign') {
      if (!isValidUUID5(cleanData.campaign_id)) {
        console.log('Audit interaction tracking skipped due to UUID8 in campaign_id:', cleanData.campaign_id);
        return;
      }
    }
    
    // Determine specific event name based on interaction type
    let eventName = 'audit_interaction_unknown';
    let interactionData = {};
    
    if (data.ctaPosition && data.ctaText) {
      // CTA button click
      eventName = 'audit_cta_clicked';
      interactionData = {
        cta_position: data.ctaPosition,
        cta_text: data.ctaText,
        button_type: 'cta',
      };
    } else if (data.clickType) {
      // Other interaction types
      switch (data.clickType) {
        case 'amazon_listing_link':
          eventName = 'audit_amazon_listing_clicked';
          interactionData = {
            product_url: data.productUrl || 'not_provided',
            product_title: data.productTitle || 'not_provided',
            link_type: 'product_listing',
          };
          break;
        case 'header_logo_link':
          eventName = 'audit_logo_clicked';
          interactionData = {
            logo_url: data.logoUrl || 'not_provided',
            logo_position: data.logoPosition || 'not_provided',
            link_type: 'brand_logo',
          };
          break;
        case 'case_study_text_link':
          eventName = 'audit_case_study_link_clicked';
          interactionData = {
            case_study_url: data.caseStudyUrl || 'not_provided',
            case_study_key: data.caseStudyKey || 'not_provided',
            link_text: data.linkText || 'not_provided',
            link_type: 'case_study_text',
          };
          break;
        case 'case_study_image_link':
          eventName = 'audit_case_study_image_clicked';
          interactionData = {
            case_study_url: data.caseStudyUrl || 'not_provided',
            case_study_key: data.caseStudyKey || 'not_provided',
            link_type: 'case_study_image',
          };
          break;
        case 'testimonial_text_link':
          eventName = 'audit_testimonial_link_clicked';
          interactionData = {
            testimonial_url: data.testimonialUrl || 'not_provided',
            testimonial_title: data.testimonialTitle || 'not_provided',
            testimonial_index: data.testimonialIndex || 'not_provided',
            link_type: 'testimonial_text',
          };
          break;
        case 'testimonial_image_link':
          eventName = 'audit_testimonial_image_clicked';
          interactionData = {
            testimonial_url: data.testimonialUrl || 'not_provided',
            testimonial_title: data.testimonialTitle || 'not_provided',
            testimonial_index: data.testimonialIndex || 'not_provided',
            link_type: 'testimonial_image',
          };
          break;
        case 'client_website_link':
          eventName = 'audit_client_website_clicked';
          interactionData = {
            website_url: data.websiteUrl || 'not_provided',
            link_position: data.linkPosition || 'not_provided',
            link_type: 'client_website',
          };
          break;
        default:
          eventName = 'audit_generic_click';
          interactionData = {
            click_type: data.clickType,
            link_type: 'generic',
          };
      }
    }
    
    // Create comprehensive event data
    const eventData = stringifyComplexProperties({
      ...cleanData,
      ...interactionData,
      event_type: 'interaction',
      page_type: 'audit',
      original_data: JSON.stringify(data),
    });
    
    posthog.capture(eventName, eventData);
    console.log(`Tracked ${eventName} for client:`, cleanData.client_name);
    
  } catch (error) {
    console.error('Failed to track audit interaction:', error);
  }
};

/**
 * Track general events with enhanced validation
 * Automatically handles complex object properties by stringifying them
 *
 * @param {string} eventName - The name of the event
 * @param {Object} properties - Event properties (objects and arrays will be stringified)
 */
export const trackEvent = (eventName, properties = {}) => {
  if (typeof window === 'undefined' || isBot()) {
    console.log('Skipping event tracking: bot detected or server-side');
    return;
  }

  // Check UUID validation before proceeding with tracking
  if (!shouldAllowTracking()) {
    console.log(`Event tracking skipped due to UUID8 detection: ${eventName}`);
    return;
  }

  if (!isInitialized) {
    console.warn('PostHog not initialized for event tracking, trying to initialize...');
    initPostHog();
    if (!isInitialized) {
      console.error('Failed to initialize PostHog for event tracking');
      return;
    }
  }

  try {
    console.log(`Tracking event: ${eventName}`, properties);

    // Enhanced event name validation
    const cleanEventName = eventName?.toString()?.trim() || 'unnamed_event';

    // Check for UUID8 in properties
    if (properties) {
      for (const [key, value] of Object.entries(properties)) {
        if (typeof value === 'string' && (key.toLowerCase().includes('uuid') || key.toLowerCase().includes('campaign'))) {
          if (!isValidUUID5(value)) {
            console.log(`Event tracking skipped due to UUID8 in property '${key}':`, value);
            return;
          }
        }
      }
    }

    // Stringify complex properties before sending to prevent [object Object] display
    const cleanedProperties = stringifyComplexProperties({
      ...properties,
      event_source: 'audit_app',
      timestamp: new Date().toISOString(),
      url_path: window.location.pathname,
      user_agent_summary: navigator.userAgent.substring(0, 100), // Truncated for cleaner data
    });

    posthog.capture(cleanEventName, cleanedProperties);
    console.log(`Event ${cleanEventName} tracked successfully`);
  } catch (error) {
    console.error(`Failed to track event ${eventName}:`, error);
  }
};

/**
 * Get PostHog instance (for direct access if needed)
 */
export const getPostHog = () => {
  return posthog;
};

/**
 * Debug function to log all available event types
 * Useful for development and documentation
 */
export const getAuditEventTypes = () => {
  return {
    pageView: {
      eventName: 'audit_page_viewed',
      description: 'Tracked when user views an audit page',
      requiredData: ['campaignId', 'userEmail', 'sellerId', 'clientName', 'auditSlug', 'companyName']
    },
    interactions: {
      'audit_cta_clicked': 'CTA button clicks',
      'audit_amazon_listing_clicked': 'Amazon product listing link clicks',
      'audit_logo_clicked': 'Brand logo clicks',
      'audit_case_study_link_clicked': 'Case study text link clicks',
      'audit_case_study_image_clicked': 'Case study image clicks',
      'audit_testimonial_link_clicked': 'Testimonial text link clicks',
      'audit_testimonial_image_clicked': 'Testimonial image clicks',
      'audit_client_website_clicked': 'Client website link clicks',
      'audit_generic_click': 'Other clickable elements'
    },
    dataFields: {
      required: ['campaign_id', 'seller_id', 'client_name', 'audit_slug', 'company_name'],
      optional: ['user_email', 'audit_id', 'product_url', 'testimonial_url', 'case_study_url'],
      metadata: ['timestamp', 'url_path', 'referrer', 'event_type', 'page_type']
    }
  };
};

/**
 * Validate tracking configuration 
 * Returns health check of PostHog setup
 */
export const validateTrackingSetup = () => {
  const health = {
    isInitialized: isInitialized,
    hasApiKey: !!process.env.NEXT_PUBLIC_POSTHOG_KEY,
    isBot: isBot(),
    isBrowser: typeof window !== 'undefined',
    timestamp: new Date().toISOString()
  };
  
  console.log('PostHog Tracking Health Check:', health);
  return health;
};

export default {
  initPostHog,
  trackAuditPageView,
  trackAuditInteraction,
  trackEvent,
  getPostHog,
  isBot,
  isValidUUID5,
  shouldAllowTracking,
  stringifyComplexProperties,
  validateTrackingSetup,
  getAuditEventTypes,
};