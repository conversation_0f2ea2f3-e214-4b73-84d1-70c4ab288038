# Automatic Page Tracking Disabled

## Changes Made

I've disabled automatic page leave and visit events in your PostHog configuration to give you full control over when page tracking occurs.

## What Was Changed

### 1. PostHog Configuration Updated

**Before:**
```javascript
posthog.init(api<PERSON><PERSON>, {
  capture_pageview: true,  // ✅ Was enabled
  capture_pageleave: true, // ✅ Was enabled
  autocapture: true,
  // ... other settings
});
```

**After:**
```javascript
posthog.init(api<PERSON><PERSON>, {
  capture_pageview: false,  // ❌ Now disabled
  capture_pageleave: false, // ❌ Now disabled
  autocapture: true,        // ✅ Still enabled (click tracking)
  // ... other settings
});
```

### 2. New Manual Page Tracking Function

Added `trackPageView()` function for when you want to manually track specific pages:

```javascript
import { trackPageView } from './src/utils/posthog';

// Manual page tracking with UUID validation
trackPageView({
  page_type: 'audit',
  campaign_id: 'your-uuid5-here',
  custom_data: 'any additional data'
});
```

## What This Means

### ❌ **Disabled (No Longer Automatic):**
- Page visit events (`$pageview`)
- Page leave events (`$pageleave`)
- Automatic navigation tracking

### ✅ **Still Enabled:**
- Click tracking (`autocapture: true`)
- Manual event tracking (`trackEvent`, `trackAuditPageView`, etc.)
- Session recording
- All your custom tracking functions

### 🔒 **UUID5 Validation Still Applied:**
- Manual `trackPageView()` still checks for UUID5-only validation
- All other tracking functions maintain UUID5-only validation
- Non-UUID5 parameters still block tracking

## How to Use Manual Page Tracking

### Option 1: Track Specific Pages Only
```javascript
// In your audit page component
import { trackPageView } from '@/utils/posthog';

useEffect(() => {
  // Only track audit pages manually
  if (shouldTrackThisPage) {
    trackPageView({
      page_type: 'audit',
      audit_slug: auditSlug,
      client_name: clientName
    });
  }
}, []);
```

### Option 2: Track All Pages Manually
```javascript
// In your layout or _app.js
import { useRouter } from 'next/router';
import { trackPageView } from '@/utils/posthog';

const router = useRouter();

useEffect(() => {
  const handleRouteChange = (url) => {
    trackPageView({
      page_url: url,
      timestamp: new Date().toISOString()
    });
  };

  router.events.on('routeChangeComplete', handleRouteChange);
  
  return () => {
    router.events.off('routeChangeComplete', handleRouteChange);
  };
}, [router.events]);
```

### Option 3: Conditional Page Tracking
```javascript
// Track only when valid UUID5 is present
import { shouldAllowTracking, trackPageView } from '@/utils/posthog';

useEffect(() => {
  if (shouldAllowTracking()) {
    trackPageView({
      page_type: 'audit',
      tracking_allowed: true
    });
  } else {
    console.log('Page tracking skipped due to non-UUID5 parameters');
  }
}, []);
```

## Benefits of Manual Control

### 🎯 **Precise Control:**
- Track only the pages you want
- Add custom data to page views
- Apply business logic before tracking

### 🔒 **Enhanced Security:**
- UUID5 validation applied to manual page tracking
- No unwanted automatic events
- Full control over what data is sent

### 📊 **Better Data Quality:**
- Meaningful page view events only
- Custom properties on each page view
- Reduced noise in PostHog dashboard

### 🚀 **Performance:**
- Fewer automatic events
- Reduced PostHog API calls
- Better page load performance

## Console Output

You'll now see:
```
PostHog initialized successfully - automatic page tracking disabled
```

Instead of automatic page events, you'll only see events when you manually call:
```
Tracking manual page view with data: { page_type: 'audit', ... }
Manual page view tracked successfully
```

## Migration Guide

### If You Want Some Automatic Tracking Back:

1. **Re-enable specific automatic tracking:**
   ```javascript
   // In posthog.js, change back to:
   capture_pageview: true,  // For automatic page views
   capture_pageleave: false, // Keep page leave disabled
   ```

2. **Use Next.js router events:**
   ```javascript
   // Add to your _app.js or layout
   router.events.on('routeChangeComplete', (url) => {
     trackPageView({ url });
   });
   ```

### If You Want All Automatic Tracking Back:
```javascript
// In posthog.js, change back to:
capture_pageview: true,
capture_pageleave: true,
```

## Summary

✅ **Automatic page leave and visit events are now disabled**
✅ **Manual page tracking function added with UUID5 validation**
✅ **Click tracking and custom events still work normally**
✅ **Full control over when and what page data is tracked**

Your PostHog tracking is now more precise and only tracks what you explicitly want to track, while maintaining the UUID5-only validation for all tracking functions.
