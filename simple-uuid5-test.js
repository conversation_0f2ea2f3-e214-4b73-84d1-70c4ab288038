/**
 * Simple UUID5-Only Validation Test
 * 
 * Tests the UUID5-only validation logic directly without importing the full PostHog module
 */

const { validate: uuidValidate, version: uuidVersion, v4: uuidv4, v5: uuidv5, v7: uuidv7 } = require('uuid');

// Simplified version of the validation function
const isValidUUID5Only = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true; // Allow empty/missing UUIDs
  
  // Use uuid library's built-in validation
  if (!uuidValidate(uuid)) {
    return true; // Allow non-UUID strings to pass through
  }
  
  try {
    const version = uuidVersion(uuid);
    
    if (version === 5) {
      console.log('Valid UUID5 detected - allowing tracking:', uuid);
      return true; // Allow only UUID5
    }
    
    // Block all other UUID versions (0, 1, 2, 3, 4, 6, 7, 8, 15)
    console.log(`UUID version ${version} detected - blocking tracking (only UUID5 allowed):`, uuid);
    return false;
  } catch (error) {
    console.error('Error checking UUID version:', error);
    return true; // Allow on error to prevent breaking functionality
  }
};

console.log('🔒 UUID5-ONLY Validation Test\n');
console.log('=' .repeat(50));
console.log('📋 RULE: Only UUID5 allowed, all others blocked');
console.log('=' .repeat(50));

// Test cases
const testCases = [
  {
    name: 'UUID5 (Generated)',
    uuid: uuidv5('test-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: true,
    description: 'Should be ALLOWED'
  },
  {
    name: 'UUID5 (Another)',
    uuid: uuidv5('summer-2024', '1b671a64-40d5-491e-99b0-da01ff1f3341'),
    expected: true,
    description: 'Should be ALLOWED'
  },
  {
    name: 'UUID4 (Random)',
    uuid: uuidv4(),
    expected: false,
    description: 'Should be BLOCKED'
  },
  {
    name: 'UUID7 (Time-based)',
    uuid: uuidv7(),
    expected: false,
    description: 'Should be BLOCKED'
  },
  {
    name: 'Your Original UUID7',
    uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0',
    expected: false,
    description: 'Should be BLOCKED'
  },
  {
    name: 'Simulated UUID8',
    uuid: '0198c3db-6e26-8894-aea5-3dc516a689c0',
    expected: false,
    description: 'Should be BLOCKED'
  },
  {
    name: 'Simulated UUID1',
    uuid: '550e8400-e29b-11d4-a716-************',
    expected: false,
    description: 'Should be BLOCKED'
  },
  {
    name: 'NIL UUID',
    uuid: '00000000-0000-0000-0000-000000000000',
    expected: false,
    description: 'Should be BLOCKED'
  },
  {
    name: 'Non-UUID String',
    uuid: 'campaign-123',
    expected: true,
    description: 'Should be ALLOWED'
  },
  {
    name: 'Empty String',
    uuid: '',
    expected: true,
    description: 'Should be ALLOWED'
  }
];

console.log('\n📝 Running Tests:\n');

let passed = 0;
let failed = 0;

testCases.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}:`);
  console.log(`   UUID: ${test.uuid}`);
  console.log(`   Expected: ${test.expected ? 'ALLOWED' : 'BLOCKED'}`);
  
  const result = isValidUUID5Only(test.uuid);
  const success = result === test.expected;
  
  console.log(`   Result: ${result ? 'ALLOWED' : 'BLOCKED'} ${success ? '✅' : '❌'}`);
  
  if (success) {
    passed++;
  } else {
    failed++;
    console.log(`   ⚠️  FAILED: Expected ${test.expected}, got ${result}`);
  }
  
  console.log('');
});

// Summary
console.log('📊 Results:\n');
console.log(`✅ Passed: ${passed}/${testCases.length}`);
console.log(`❌ Failed: ${failed}/${testCases.length}`);
console.log(`Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! UUID5-only validation working correctly.');
} else {
  console.log('\n⚠️  Some tests failed.');
}

// Generate valid UUID5 examples
console.log('\n🔧 Valid UUID5 Examples:\n');

const campaigns = ['summer-2024', 'winter-sale', 'black-friday'];
const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';

campaigns.forEach((campaign, index) => {
  const uuid5 = uuidv5(campaign, namespace);
  console.log(`${index + 1}. Campaign: "${campaign}"`);
  console.log(`   UUID5: ${uuid5}`);
  console.log(`   Validation: ${isValidUUID5Only(uuid5) ? '✅ ALLOWED' : '❌ BLOCKED'}`);
  console.log('');
});

console.log('💡 Key Changes:\n');
console.log('• ✅ UUID5 (name-based): ALLOWED');
console.log('• ❌ UUID1 (time-based): BLOCKED');
console.log('• ❌ UUID2 (DCE security): BLOCKED');
console.log('• ❌ UUID3 (MD5 name-based): BLOCKED');
console.log('• ❌ UUID4 (random): BLOCKED');
console.log('• ❌ UUID6 (reordered time): BLOCKED');
console.log('• ❌ UUID7 (Unix time): BLOCKED');
console.log('• ❌ UUID8 (experimental): BLOCKED');
console.log('• ✅ Non-UUID strings: ALLOWED');

console.log('\n⚠️  Impact:\n');
console.log('• Your original UUID7 will now be BLOCKED');
console.log('• Only deterministic UUID5 will be allowed');
console.log('• Use uuidv5(name, namespace) to generate valid UUIDs');

console.log('\n' + '=' .repeat(50));
console.log('✨ UUID5-only validation test complete!');
