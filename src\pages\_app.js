import React from 'react';
import PropTypes from 'prop-types';
import Head from 'next/head';

import Providers from '../app/provider';
import Page from '../components/Page';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import 'aos/dist/aos.css';

export default function App({ Component, pageProps }) {
  return (
    <Providers>
      <React.Fragment>
        <Head>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, shrink-to-fit=no"
          />
          <title>Equal Collective</title>
        </Head>
        <Page>
          <Component {...pageProps} />
        </Page>
      </React.Fragment>
    </Providers>
  );
}

App.propTypes = {
  Component: PropTypes.elementType.isRequired,
  pageProps: PropTypes.object.isRequired,
};
