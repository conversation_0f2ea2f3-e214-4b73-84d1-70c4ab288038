'use client';

import React, { useState, useMemo, useEffect } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  CssBaseline,
  Divider,
  Tooltip,
  useMediaQuery,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import CampaignIcon from '@mui/icons-material/Campaign';
import CloudIcon from '@mui/icons-material/Cloud';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import ContactsIcon from '@mui/icons-material/Contacts';
import RateReviewIcon from '@mui/icons-material/RateReview';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import { useRouter } from 'next/navigation';
import { useAdminTheme } from './AdminThemeContext';
import { Image } from '@mui/icons-material';

const drawerWidth = 240;

const menuItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/jeff/dashboard',
    id: 'dashboard',
  },
  {
    text: 'All Clients',
    icon: <PeopleIcon />,
    path: '/jeff/admin/client',
    id: 'clients',
  },
  {
    text: 'Campaigns',
    icon: <CampaignIcon />,
    path: '/jeff/admin/campaigns',
    id: 'campaigns',
  },
  {
    text: 'AWS Instances',
    icon: <CloudIcon />,
    path: '/jeff/admin/aws-instances',
    id: 'aws',
  },
  {
    text: 'Lead Management',
    icon: <ContactsIcon />,
    path: '/jeff/admin/leads',
    id: 'leads',
  },
  {
    text: 'Lex Reviews',
    icon: <RateReviewIcon />,
    path: '/jeff/admin/reviews',
    id: 'reviews',
  },
  {
    text: 'Lex Image Generator',
    icon: <Image />,
    path: '/jeff/admin/lex-image-gen',
    id: 'lex-image-gen',
  },
  {
    text: 'CSV Matching',
    icon: <RateReviewIcon />,
    path: '/jeff/admin/csv-matching',
    id: 'csv-matching',
  },
  {
    text: 'API Access',
    icon: <VpnKeyIcon />,
    path: '/jeff/admin/api-access',
    id: 'api-access',
  },
];

const JeffAdminPanel = ({ children, title = 'Admin Panel' }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [currentPath, setCurrentPath] = useState(typeof window !== 'undefined' ? window.location.pathname : '');
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { mode: adminThemeMode, toggleTheme: toggleAdminTheme } =
    useAdminTheme();
  const isDarkMode = adminThemeMode === 'dark';

  // Update currentPath when component mounts and when route changes
  useEffect(() => {
    setCurrentPath(window.location.pathname);
    
    // Listen for route changes
    const handleRouteChange = (url) => {
      setCurrentPath(url);
    };

    window.addEventListener('popstate', () => handleRouteChange(window.location.pathname));
    return () => {
      window.removeEventListener('popstate', () => handleRouteChange(window.location.pathname));
    };
  }, []);

  // Close drawer when clicking on a menu item on mobile
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Close drawer when navigating
  const handleNavigation = (path) => {
    if (isMobile) {
      setMobileOpen(false);
    }
    
    setCurrentPath(path);
    if (path === '/jeff/dashboard') {
      window.location.href = path; // Full page reload for dashboard
    } else {
      router.push(path);
    }
  };

  const currentTab = useMemo(() => {
    const matchingItem = menuItems.find((item) => currentPath.startsWith(item.path));
    return matchingItem ? matchingItem.id : 'dashboard';
  }, [currentPath]); // Now depends on currentPath state instead of window.location.pathname

  const drawer = (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
        transition: theme.transitions.create(['background-color'], {
          duration: theme.transitions.duration.standard,
        }),
      }}
    >
      <Toolbar
        sx={{
          borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : theme.palette.divider
          }`,
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{
            fontWeight: 600,
            letterSpacing: '0.5px',
            color: isDarkMode ? '#f0f0f0' : '#333333',
          }}
        >
          Jeff Admin
        </Typography>
      </Toolbar>
      <Divider />
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              onClick={() => handleNavigation(item.path)}
              sx={{
                py: 1.5,
                px: 2,
                borderRadius: '4px',
                mx: 1,
                mb: 0.5,
                '&:hover': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.08)'
                    : 'rgba(0, 0, 0, 0.08)',
                },
                '&.Mui-selected': {
                  backgroundColor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.16)'
                    : 'rgba(0, 0, 0, 0.16)',
                  '&:hover': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.24)'
                      : 'rgba(0, 0, 0, 0.24)',
                  },
                },
              }}
              selected={currentTab === item.id}
            >
              <ListItemIcon
                sx={{
                  minWidth: 40,
                  color: isDarkMode ? '#f0f0f0' : '#333333',
                  ...(currentTab === item.id && {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  }),
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{
                  fontSize: '0.95rem',
                  fontWeight: currentTab === item.id ? 600 : 500,
                  color:
                    currentTab === item.id
                      ? isDarkMode
                        ? '#90CAF9'
                        : '#1976d2'
                      : 'inherit',
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      <Box
        sx={{
          p: 2,
          borderTop: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : theme.palette.divider
          }`,
        }}
      >
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ display: 'block', textAlign: 'center', mb: 1 }}
        >
          © {new Date().getFullYear()} Jeff Admin
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box
      sx={{
        display: 'flex',
        backgroundColor: isDarkMode ? '#1a2035' : '#f5f7fa',
        minHeight: '100vh',
        transition: theme.transitions.create(['background-color'], {
          duration: theme.transitions.duration.standard,
        }),
      }}
    >
      <CssBaseline />
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          color: isDarkMode ? '#f0f0f0' : '#333333',
          borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : theme.palette.divider
          }`,
          transition: theme.transitions.create(
            ['background-color', 'color', 'box-shadow'],
            {
              duration: theme.transitions.duration.standard,
            },
          ),
        }}
      >
        <Toolbar sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { sm: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                fontWeight: 500,
                letterSpacing: '0.25px',
              }}
            >
              {title}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip
              title={
                isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'
              }
            >
              <IconButton
                sx={{
                  ml: 1,
                  bgcolor: isDarkMode
                    ? 'rgba(255, 255, 255, 0.04)'
                    : 'rgba(0, 0, 0, 0.04)',
                  '&:hover': {
                    bgcolor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.08)',
                  },
                  borderRadius: 1,
                  p: { xs: 1, sm: 1.5 },
                  transition: theme.transitions.create(['background-color'], {
                    duration: theme.transitions.duration.shortest,
                  }),
                }}
                onClick={toggleAdminTheme}
                color="inherit"
                aria-label="toggle dark mode"
              >
                {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
                <Typography
                  variant="body2"
                  sx={{
                    ml: 1,
                    display: { xs: 'none', sm: 'block' },
                    fontWeight: 500,
                  }}
                >
                  {isDarkMode ? 'Light Mode' : 'Dark Mode'}
                </Typography>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="admin navigation"
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
              color: isDarkMode ? '#f0f0f0' : '#333333',
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
              color: isDarkMode ? '#f0f0f0' : '#333333',
              borderRight: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.12)' : theme.palette.divider
              }`,
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          marginTop: '64px',
          backgroundColor: isDarkMode ? '#1a2035' : '#f5f7fa',
          transition: theme.transitions.create(['background-color'], {
            duration: theme.transitions.duration.standard,
          }),
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default JeffAdminPanel;
