'use client';

import React, { useEffect } from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { colors } from '@mui/material';
import { Container } from '@mui/material';
import { alpha } from '@mui/material/styles';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';

import { BasicTable } from 'views/GigaBrains/GigaBrains';
import PPCAudit from './PPCAudit';
import attachUtmSource from '../Utils/attachUtmSource';
import getUrlConnector from '../Utils/getUrlConnector';
import { trackAuditPageView, trackAuditInteraction } from '../../../utils/posthog';

const JeffAudit = ({ companyData, originalParams }) => {
  const searchParams = useSearchParams();
  const paramName = searchParams.get('pdf');

  let source = searchParams.get('source');
  source = source === 'pdf' ? 'utm_source=pdf' : 'utm_source=web';

  const mdDownMatches = useMediaQuery((theme) => theme.breakpoints.down('md'));
  const smUpMatches = useMediaQuery((theme) => theme.breakpoints.up('sm'));
  const smDownMatches = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const clientData = companyData && companyData.user ? companyData.user : {};
  const data = companyData && companyData.auditReport;

  let title =
    companyData?.prospectDetails &&
    companyData.prospectDetails.humanizedProspectProductTitle
      ? companyData.prospectDetails.humanizedProspectProductTitle
      : '';

  title = title
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');

  const rows =
    data &&
    Object.keys(data).map((key) => {
      return {
        characteristic: key,
        painPoints: data[key].PAIN_POINT,
        improvements: data[key].Improvements,
        benefits: data[key].Benefits,
        priority: data[key].PRIORITY,
      };
    });

  // Helper function to get base tracking data (same as page load)
  const getBaseTrackingData = () => {
    return {
      campaignId: originalParams?.userData?.campaignId || 'none',
      isTracking: !!originalParams?.userData,
      hasUserData: !!originalParams?.userData,
      companyName: companyData?.companyName || '',
      auditId: companyData?.companyId || '',
      auditSlug: originalParams?.slug || '',
      userEmail: originalParams?.userData?.email || '',
      sellerId: originalParams?.userData?.sellerId || '',
      clientName: originalParams?.userData?.clientId || '',
    };
  };

  // Helper function to track CTA clicks
  const handleCtaClick = (ctaPosition, ctaText) => {
    if (originalParams) {
      const trackingData = {
        ...getBaseTrackingData(),
        ctaPosition,
        ctaText,
      };
      
      console.log('JeffAudit: Tracking CTA click:', trackingData);
      trackAuditInteraction(trackingData);
    }
  };

  // Generic click handler for all other clickable elements
  const handleGenericClick = (clickType, additionalData = {}) => {
    if (originalParams) {
      const trackingData = {
        ...getBaseTrackingData(),
        clickType,
        ...additionalData,
      };
      
      console.log('JeffAudit: Tracking generic click:', trackingData);
      trackAuditInteraction(trackingData);
    }
  };

  useEffect(() => {
    document.title = `Amazon Audit by ${clientData?.name || ''}`;
    
    // Track page view when component mounts
    if (companyData && originalParams) {
      const trackingData = {
        campaignId: originalParams.userData?.campaignId || 'none',
        isTracking: !!originalParams.userData,
        hasUserData: !!originalParams.userData,
        companyName: companyData.companyName || '',
        auditId: companyData.companyId || '',
        auditSlug: originalParams.slug || '',
        userEmail: originalParams.userData?.email || '',
        sellerId: originalParams.userData?.sellerId || '',
        clientName: originalParams.userData?.clientId || '',
      };
      
      console.log('JeffAudit: Tracking page view with data:', trackingData);
      trackAuditPageView(trackingData);
    }
  }, [clientData?.name, companyData, originalParams]);

  return (
    <>
      {/* <AppBar
        position={'sticky'}
        sx={{
          top: 0,
          backgroundColor: trigger
            ? theme.palette.background.paper
            : 'transparent',
        }}
        elevation={trigger ? 1 : 0}
      >
        <Container style={{padding: '8px 0'}}>
          <Box
            display={'flex'}
            justifyContent={'space-between'}
            alignItems={'center'}
          >
            <Box display={'flex'} title="theFront" width={{ xs: 100, md: 120 }}>
              <Image src={clientData?.logo || ''} width={120} height={28} />
            </Box>
          </Box>
        </Container>
      </AppBar> */}

      {/* header and logo */}
      {clientData?.name === 'Jeff Admin' ? (
        <Box
          id="header"
          marginBottom={4}
          paddingX={smUpMatches ? 8 : 0}
          sx={{ width: '100%' }}
          bgcolor={colors.grey[300]}
          height={250}
          display={'flex'}
          gap={20}
          justifyContent={'center'}
          alignItems={'center'}
        >
          <Box display={'flex'} alignItems={'center'} gap={4}>
            <Box>
              <img src="/images/jeff/persona.png" width={200} alt={'jeff'} />
            </Box>

            <Typography
              variant="h2"
              component={'span'}
              color="primary"
              sx={{
                fontWeight: 700,
                display: 'inline',
                background: `linear-gradient(180deg, transparent 82%, ${alpha(
                  colors.purple[200],
                  0.3,
                )} 0%)`,
              }}
            >
              Jeff
            </Typography>
          </Box>

          <Typography
            variant="h2"
            style={{ fontWeight: 'bold' }}
            textAlign={'center'}
          >
            Equal Collective
          </Typography>
        </Box>
      ) : (
        <Box
          id="header"
          display={'flex'}
          style={{ width: '100%' }}
          href={clientData?.website ? attachUtmSource(clientData.website) : ''}
          component={clientData?.website ? 'a' : 'div'}
          target="_blank"
          onClick={clientData?.website ? () => handleGenericClick('header_logo_link', {
            logoUrl: clientData?.website || '',
            logoPosition: 'top'
          }) : undefined}
          data-ph-capture-attribute="false"
          height={
            paramName
              ? '16vh'
              : mdDownMatches && smUpMatches
                ? '18vh'
                : smDownMatches
                  ? '14vh'
                  : '250px'
          }
        >
          {paramName ? (
            <img
              src={clientData.logo}
              style={{ width: '100%', height: 'auto' }}
            />
          ) : (
            <Image
              src={clientData.logo || ''} // Default fallback image (optional, if srcSet fails or is unsupported)
              style={{ width: '100%', height: 'auto', objectFit: 'fill' }}
              alt="Responsive image"
              layout="responsive"
              width={500} // This can be any value since it will adjust to screen size
              height={250} // This can be any value as the height will adjust based on the width
              srcSet={`
        ${clientData.mbLogo} 700w,
        ${clientData.tableLogo} 1400w,
        ${clientData.tableLogo} 1900w
      `}
              sizes="(max-width: 699px) 100vw, (max-width: 1399px) 100vw, 100vw"
            />
          )}
        </Box>
      )}

      {/* container */}
      <Container maxWidth={1400} id="outer-container">
        {companyData && rows ? (
          <Box
            sx={{ width: '100%' }}
            padding={6}
            display={'flex'}
            flexDirection={'column'}
          >
            {/* amazon listing page link */}
            <Box
              id="link-title"
              marginTop={6}
              marginBottom={4}
              paddingX={smUpMatches ? 8 : 0}
              sx={{ width: '100%' }}
            >
              <Typography variant={'h4'} fontWeight={500}>
                Amazon Listing Page:{' '}
                <Link
                  href={
                    companyData?.productUrl
                      ? `${companyData?.productUrl}${getUrlConnector(
                        companyData?.productUrl,
                      )}${source}`
                      : ''
                  }
                  target={'_blank'}
                  onClick={() => handleGenericClick('amazon_listing_link', {
                    productUrl: companyData?.productUrl || '',
                    productTitle: companyData?.prospectDetails?.humanizedProspectProductTitle || '-'
                  })}
                  data-ph-capture-attribute="false"
                >
                  {companyData?.prospectDetails
                    ?.humanizedProspectProductTitle || '-'}
                </Link>
              </Typography>
            </Box>

            {/* amazon page img */}
            <Box
              id="hero-image"
              marginY={6}
              paddingX={smUpMatches ? 8 : 0}
              style={{ width: '100%' }}
            >
              <img
                src={companyData?.pageImage ? companyData.pageImage : ''}
                alt="pageImage"
                loading="eager"
                style={{ width: '100%', height: 'auto' }}
              />
            </Box>

            {/* PPC audit */}
            {companyData.ppcReport && (
              <PPCAudit
                data={companyData?.ppcReport}
                title={title}
                mdDownMatches={mdDownMatches}
                smUpMatches={smUpMatches}
                smDownMatches={smDownMatches}
              />
            )}

            {/* audit table heading */}
            <Box
              id="company-title"
              marginTop={6}
              marginBottom={4}
              paddingX={smUpMatches ? 8 : 0}
              sx={{ width: '100%' }}
            >
              <Typography
                variant="h2"
                textAlign={'center'}
                style={{ fontWeight: '400' }}
              >
                {/*{`Listing ${
                  clientData.slug === 'riverguide-158373'
                    ? 'Optimization'
                    : 'Optimisation'
                }  Audit - "${title}"`}*/}

                {`Listing Optimization Audit - ${title}`}
              </Typography>
            </Box>

            {/* audit table */}
            <BasicTable
              rows={rows}
              smUpMatches={smUpMatches}
              clientData={clientData}
            />

            {/* cta1 */}
            <Box
              id="cta1"
              display="flex"
              flexDirection={{ xs: 'column' }}
              alignItems={{ xs: 'stretched', sm: 'center' }}
              justifyContent={'center'}
              marginTop={6}
              paddingY={2}
              bgcolor={alpha(colors.indigo[200], 0.2)}
            >
              <Typography
                variant="h5"
                color="text.primary"
                align={'center'}
                sx={{ fontWeight: 700, paddingBottom: 2 }}
              >
                {clientData?.offerLine
                  ? clientData?.offerLine
                  : `Would love to unlock more insights by doing a seller central
                level audit on a quick call`}
              </Typography>

              <Button
                component={'a'}
                variant="contained"
                color="primary"
                size="large"
                sx={{
                  padding: '16px 48px',
                  fontSize: '24px',
                  fontWeight: 'bold',
                }}
                fullWidth={mdDownMatches ? true : false}
                href={
                  clientData?.ctaLink
                    ? `${clientData?.ctaLink}${getUrlConnector(
                      clientData?.ctaLink,
                    )}${source}`
                    : ''
                }
                target={'_blank'}
                onClick={() => handleCtaClick('cta1', clientData?.ctaButtonText || 'CTA Button')}
                data-ph-capture-attribute="false"
              >
                {clientData?.ctaButtonText}
              </Button>
            </Box>

            {/* case studies */}
            {clientData.caseStudies &&
            Object.keys(clientData.caseStudies).length > 0 ? (
                <div id="case-studies">
                  <Container maxWidth={'lg'}>
                    <Box
                      marginTop={8}
                      borderRadius={2}
                      paddingX={smUpMatches ? 8 : 0}
                    >
                      <Typography
                        variant="h2"
                        textAlign={'center'}
                        style={{ fontWeight: '400' }}
                      >
                      Case Studies
                      </Typography>
                    </Box>
                  </Container>

                  <Container maxWidth={'lg'}>
                    {clientData?.caseStudies &&
                    Object.keys(clientData.caseStudies)
                      .filter((_, idx) => idx < 2)
                      .map((key) => (
                        <Box
                          key={key}
                          paddingY={5}
                          paddingX={smUpMatches ? 8 : 0}
                        >
                          <Typography
                            variant="h5"
                            style={{ fontWeight: 'bold' }}
                            paddingY={2}
                          >
                            {clientData.caseStudies[key].link && (
                              <Link
                                href={
                                  clientData.caseStudies[key].link
                                    ? `${
                                      clientData.caseStudies[key].link
                                    }${getUrlConnector(
                                      clientData.caseStudies[key].link,
                                    )}${source}`
                                    : ''
                                }
                                target={'_blank'}
                                onClick={() => handleGenericClick('case_study_text_link', {
                                  caseStudyUrl: clientData.caseStudies[key].link || '',
                                  caseStudyKey: key,
                                  linkText: clientData.caseStudiesText || 'Read full case study here'
                                })}
                                data-ph-capture-attribute="false"
                              >
                                {clientData.caseStudiesText ||
                                  'Read full case study here'}
                              </Link>
                            )}
                          </Typography>

                          <Box
                            key={key}
                            marginTop={2}
                            marginBottom={6}
                            style={{ width: '100%' }}
                            component={
                              clientData.caseStudies[key].link ? 'a' : 'div'
                            }
                            href={
                              clientData.caseStudies[key].link
                                ? `${
                                  clientData.caseStudies[key].link
                                }${getUrlConnector(
                                  clientData.caseStudies[key].link,
                                )}${source}`
                                : '#'
                            }
                            target={
                              clientData.caseStudies[key].link ? '_blank' : ''
                            }
                            onClick={clientData.caseStudies[key].link ? () => handleGenericClick('case_study_image_link', {
                              caseStudyUrl: clientData.caseStudies[key].link || '',
                              caseStudyKey: key,
                              linkType: 'image'
                            }) : undefined}
                            data-ph-capture-attribute="false"
                          >
                            <img
                              src={clientData.caseStudies[key].image}
                              alt="pageImage"
                              loading="eager"
                              style={{ width: '100%', height: 'auto' }}
                            />
                          </Box>
                        </Box>
                      ))}
                  </Container>
                </div>
              ) : (
                <Container maxWidth={'md'} id="case-studies">
                  <Box
                    marginTop={8}
                    marginBottom={6}
                    borderRadius={2}
                    padding={3}
                    bgcolor={colors.grey[300]}
                  >
                    <Typography
                      variant="h4"
                      textAlign={'center'}
                      style={{ fontWeight: '400' }}
                    >
                      {
                        'The most relevant case study to this prospect from your portfolio comes here'
                      }
                    </Typography>
                  </Box>
                </Container>
              )}

            {/* cta2 */}
            <Box
              id="cta2"
              display="flex"
              flexDirection={{ xs: 'column' }}
              alignItems={{ xs: 'stretched', sm: 'center' }}
              justifyContent={'center'}
              marginTop={6}
              paddingY={2}
              bgcolor={alpha(colors.indigo[200], 0.2)}
            >
              <Typography
                variant="h5"
                color="text.primary"
                align={'center'}
                sx={{ fontWeight: 700, paddingBottom: 2 }}
              >
                {clientData?.offerLine
                  ? clientData?.offerLine
                  : `Would love to unlock more insights by doing a seller central
                level audit on a quick call`}
              </Typography>

              <Button
                component={'a'}
                variant="contained"
                color="primary"
                size="large"
                sx={{
                  padding: '16px 48px',
                  fontSize: '24px',
                  fontWeight: 'bold',
                }}
                fullWidth={mdDownMatches ? true : false}
                href={
                  clientData?.ctaLink
                    ? `${clientData?.ctaLink}${getUrlConnector(
                      clientData?.ctaLink,
                    )}${source}`
                    : ''
                }
                target={'_blank'}
                onClick={() => handleCtaClick('cta2', clientData?.ctaButtonText || 'CTA Button')}
                data-ph-capture-attribute="false"
              >
                {clientData?.ctaButtonText}
              </Button>
            </Box>

            {/* testimonials */}
            {clientData.testimonials && clientData.testimonials.length > 0 ? (
              <div id="testimonials">
                <Container maxWidth={'lg'}>
                  <Box
                    marginTop={8}
                    paddingTop={4}
                    borderRadius={2}
                    paddingX={smUpMatches ? 8 : 0}
                  >
                    <Typography
                      variant="h2"
                      textAlign={'center'}
                      style={{ fontWeight: '400' }}
                    >
                      Testimonials
                    </Typography>
                  </Box>
                </Container>

                <Container maxWidth={'lg'}>
                  {clientData?.testimonials &&
                    clientData.testimonials.map((testi, key) => (
                      <Box
                        key={key}
                        paddingY={5}
                        paddingX={smUpMatches ? 8 : 0}
                      >
                        <Typography
                          variant="h5"
                          style={{ fontWeight: 'bold' }}
                          paddingY={2}
                        >
                          {testi.video ? (
                            <Link
                              href={
                                testi.video
                                  ? `${testi.video}${getUrlConnector(
                                    testi.video,
                                  )}${source}`
                                  : ''
                              }
                              target={'_blank'}
                              onClick={() => handleGenericClick('testimonial_text_link', {
                                testimonialUrl: testi.video || '',
                                testimonialTitle: testi.title || '',
                                testimonialIndex: key
                              })}
                              data-ph-capture-attribute="false"
                            >
                              {testi.title}
                            </Link>
                          ) : (
                            testi.title
                          )}
                        </Typography>

                        <Box
                          marginTop={2}
                          marginBottom={6}
                          style={{ width: '100%' }}
                          component={testi.video ? 'a' : 'div'}
                          href={
                            testi.video
                              ? `${testi.video}${getUrlConnector(
                                testi.video,
                              )}${source}`
                              : ''
                          }
                          target={testi.video ? '_blank' : ''}
                          onClick={testi.video ? () => handleGenericClick('testimonial_image_link', {
                            testimonialUrl: testi.video || '',
                            testimonialTitle: testi.title || '',
                            testimonialIndex: key,
                            linkType: 'image'
                          }) : undefined}
                          data-ph-capture-attribute="false"
                        >
                          <img
                            src={testi.photo}
                            alt="pageImage"
                            loading="eager"
                            style={{ width: '100%', height: 'auto' }}
                          />
                        </Box>
                      </Box>
                    ))}
                </Container>
              </div>
            ) : (
              <Container maxWidth={'md'} id="testimonials">
                <Box
                  marginTop={8}
                  marginBottom={6}
                  borderRadius={2}
                  padding={3}
                  bgcolor={colors.grey[300]}
                >
                  <Typography
                    variant="h4"
                    textAlign={'center'}
                    style={{ fontWeight: '400' }}
                  >
                    {
                      'The most relevant testimonial to this prospect from your portfolio comes here'
                    }
                  </Typography>
                </Box>
              </Container>
            )}

            {/* cta3 */}
            <Box
              id="cta3"
              display="flex"
              flexDirection={{ xs: 'column' }}
              alignItems={{ xs: 'stretched', sm: 'center' }}
              justifyContent={'center'}
              marginTop={6}
              paddingY={2}
              bgcolor={alpha(colors.indigo[200], 0.2)}
            >
              <Typography
                variant="h5"
                color="text.primary"
                align={'center'}
                sx={{ fontWeight: 700, paddingBottom: 2 }}
              >
                {clientData?.offerLine
                  ? clientData?.offerLine
                  : `Would love to unlock more insights by doing a seller central
                level audit on a quick call`}
              </Typography>

              <Button
                component={'a'}
                variant="contained"
                color="primary"
                size="large"
                sx={{
                  padding: '16px 48px',
                  fontSize: '24px',
                  fontWeight: 'bold',
                }}
                fullWidth={mdDownMatches ? true : false}
                href={
                  clientData?.ctaLink
                    ? `${clientData?.ctaLink}${getUrlConnector(
                      clientData?.ctaLink,
                    )}${source}`
                    : ''
                }
                target={'_blank'}
                onClick={() => handleCtaClick('cta3', clientData?.ctaButtonText || 'CTA Button')}
                data-ph-capture-attribute="false"
              >
                {clientData?.ctaButtonText}
              </Button>
            </Box>
          </Box>
        ) : (
          !companyData &&
          !rows && (
            <Box
              display="flex"
              minHeight="80vh"
              justifyContent="center"
              sx={{ width: '100%' }}
              paddingY={6}
            >
              <Typography variant="h3">Company not found</Typography>
            </Box>
          )
        )}
      </Container>

      {/* header and logo */}
      {clientData?.name === 'Jeff Admin' ? (
        <Box
          id="header-bottom"
          marginBottom={4}
          paddingX={smUpMatches ? 8 : 0}
          sx={{ width: '100%' }}
          bgcolor={colors.grey[300]}
          height={250}
          display={'flex'}
          gap={20}
          justifyContent={'center'}
          alignItems={'center'}
        >
          <Box display={'flex'} alignItems={'center'} gap={4}>
            <Box>
              <img src="/images/jeff/persona.png" width={200} alt={'jeff'} />
            </Box>

            <Typography
              variant="h2"
              component={'span'}
              color="primary"
              sx={{
                fontWeight: 700,
                display: 'inline',
                background: `linear-gradient(180deg, transparent 82%, ${alpha(
                  colors.purple[200],
                  0.3,
                )} 0%)`,
              }}
            >
              Jeff
            </Typography>
          </Box>

          <Typography
            variant="h2"
            style={{ fontWeight: 'bold' }}
            textAlign={'center'}
          >
            Equal Collective
          </Typography>
        </Box>
      ) : (
        <Box
          id="header-bottom"
          display={'flex'}
          style={{ width: '100%' }}
          href={attachUtmSource(clientData?.website) || ''}
          component={clientData?.website ? 'a' : 'div'}
          target="_blank"
          onClick={clientData?.website ? () => handleGenericClick('header_logo_link', {
            logoUrl: clientData?.website || '',
            logoPosition: 'bottom'
          }) : undefined}
          data-ph-capture-attribute="false"
          height={
            paramName
              ? '16vh'
              : mdDownMatches && smUpMatches
                ? '18vh'
                : smDownMatches
                  ? '14vh'
                  : '250px'
          }
        >
          {paramName ? (
            <img
              src={clientData.logo}
              style={{ width: '100%', height: 'auto' }}
            />
          ) : (
            <Image
              src={clientData.logo || ''} // Default fallback image (optional, if srcSet fails or is unsupported)
              style={{ width: '100%', height: 'auto', objectFit: 'fill' }}
              alt="Responsive image"
              layout="responsive"
              width={500} // This can be any value since it will adjust to screen size
              height={250} // This can be any value as the height will adjust based on the width
              srcSet={`
        ${clientData.mbLogo} 700w,
        ${clientData.tableLogo} 1400w,
        ${clientData.tableLogo} 1900w
      `}
              sizes="(max-width: 699px) 100vw, (max-width: 1399px) 100vw, 100vw"
            />
          )}
        </Box>
      )}

      <Container maxWidth={1400}>
        {companyData && rows && (
          <Box
            sx={{ width: '100%' }}
            padding={6}
            display={'flex'}
            flexDirection={'column'}
          >
            {/* website link */}
            {clientData.website && (
              <Box
                id="client-link"
                marginTop={3}
                marginBottom={4}
                paddingX={smUpMatches ? 8 : 0}
                sx={{ width: '100%' }}
              >
                <Typography variant={'h4'} fontWeight={500} textAlign="center">
                  <Link
                    href={attachUtmSource(clientData?.website) || ''}
                    target={'_blank'}
                    onClick={() => handleGenericClick('client_website_link', {
                      websiteUrl: clientData?.website || '',
                      linkPosition: 'bottom'
                    })}
                    data-ph-capture-attribute="false"
                  >
                    {clientData?.website || ''}
                  </Link>
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Container>
    </>
  );
};

export default JeffAudit;
