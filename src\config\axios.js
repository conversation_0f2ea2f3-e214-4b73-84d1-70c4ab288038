import Axios from 'axios';
import { logger } from './logger';
import Cookies from 'js-cookie';
import { getJeffBaseURL, getSellerBotBaseURL, getBaseURL } from './api';

export const getAxiosInstance = (
  data = {
    baseUrl: '',
    cookiesKey: '',
  },
) => {
  const API_BASE_URL = data?.baseUrl || getJeffBaseURL();
  console.log('Creating axios instance with base URL:', API_BASE_URL);

  const axios = Axios.create({
    baseURL: API_BASE_URL,
  });

  axios.interceptors.request.use(
    async (config) => {
      const request = config;
      let authorization;
      // if (!window) {
      //   // const cookieStore = cookies();
      //   // authorization = cookieStore.get('authorization');
      // } else {
      authorization = Cookies.get(data?.cookiesKey || 'authorization');
      // }
      if (authorization) {
        // Ensure token has Bearer prefix
        const authHeader = authorization.startsWith('Bearer ') 
          ? authorization 
          : `Bearer ${authorization}`;
        request.headers.Authorization = authHeader;
      }
      return request;
    },
    (err) => {
      return Promise.reject(err);
    },
  );

  axios.interceptors.request.use((request) => {
    logger(
      'axios',
      `Request to ${request.method?.toUpperCase()} ${request.url} with params`,
      request.params,
      'and data',
      request.data,
    );
    return request;
  });

  axios.interceptors.response.use((response) => {
    logger(
      'axios',
      `Response from ${response.config.method?.toUpperCase()} ${
        response.config.url
      } with params`,
      response.config.params,
      'and data',
      response.config.data,
      'and got response data ->',
      response.data,
    );
    return response;
  });

  return axios;
};

export const getSellerBotAxiosInstance = (
  data = {
    cookiesKey: '',
  },
) => {
  const baseUrl = getSellerBotBaseURL();
  return getAxiosInstance({
    baseUrl,
    cookiesKey: data?.cookiesKey || 'jeff-authorization',
  });
};

export const getBrandbuddyAxiosInstance = () => {
  const baseUrl = getBaseURL();
  console.log('Creating Brandbuddy axios instance with base URL:', baseUrl);
  
  const instance = getAxiosInstance({
    baseUrl,
    cookiesKey: 'authorization',
  });

  // Add response error interceptor specifically for Brandbuddy
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('Brandbuddy API Error:', {
        status: error.response?.status,
        url: error.config?.url,
        method: error.config?.method,
        data: error.response?.data,
      });
      return Promise.reject(error);
    }
  );

  return instance;
};

export default getAxiosInstance;
