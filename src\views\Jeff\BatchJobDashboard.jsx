'use client';

import React, { useMemo, useState } from 'react';
import GenerateGoogleSheetButton from './GenerateGoogleSheetButton';

import {
  Box,
  Container,
  Typography,
  Stack,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  Select,
  MenuItem,
  InputLabel,
  Paper,
  useTheme,
} from '@mui/material';

import JobTable from './JobTable';
import { useAdminTheme } from './AdminThemeContext';

import { useDropzone } from 'react-dropzone';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import getAxiosInstance from '../../config/axios';
import { API_ENDPOINTS, getJeffBaseURL } from '../../config/api';

const fetchUploadCSV = async (data) => {
  const formData = new FormData();
  formData.append('csvFile', data.csvFile);
  formData.append('campaignId', data.campaignData.id);

  let PPCAudit = data.isPPCAudit ? '?ppcAudit=true' : '';

  const response = await getAxiosInstance({
    baseUrl: getJeffBaseURL(),
    cookiesKey: 'jeff-authorization',
  }).post(`${API_ENDPOINTS.JOBS}/${data.userId}${PPCAudit}`, formData);
  return response.data;
};

const BatchJobDashboard = ({
  imitatedClient,
  jobs,
  campaigns,
  selectedIds,
  setSelectedIds,
  isLoading
}) => {
  const queryClient = useQueryClient();
  const [csvFile, setCsvFile] = useState(null);
  const [isPPCAudit, setIsPPCAudit] = useState(false);
  const [campaignData, setCampaignData] = useState({ id: '', campaign: '' });
  const theme = useTheme();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  const baseStyle = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '40px 60px',
    borderWidth: 2,
    borderRadius: 4,
    borderColor: isDarkMode ? '#467de3' : '#3367d6',
    borderStyle: 'dashed',
    backgroundColor: isDarkMode ? '#1a2138' : '#f5f8ff',
    color: isDarkMode ? '#77a0ff' : '#3367d6',
    outline: 'none',
    transition: 'border .24s ease-in-out, background-color .24s ease-in-out',
    cursor: 'pointer',
    boxShadow: isDarkMode ? '0 4px 12px rgba(0,0,0,0.2)' : '0 2px 8px rgba(0,0,0,0.05)',
  };

  const focusedStyle = {
    borderColor: isDarkMode ? '#77a0ff' : '#4285f4',
    backgroundColor: isDarkMode ? '#23305c' : '#eef4ff',
  };

  const acceptStyle = {
    borderColor: isDarkMode ? '#3acf8e' : '#00c853',
    backgroundColor: isDarkMode ? '#1a3a2a' : '#e8f5e9',
  };

  const rejectStyle = {
    borderColor: isDarkMode ? '#ff5252' : '#d50000',
    backgroundColor: isDarkMode ? '#3a1a1a' : '#ffebee',
  };

  const { mutateAsync, isPending } = useMutation({
    mutationFn: fetchUploadCSV,
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: ['jeff-jobs', { clientId: imitatedClient.id }],
      }),
  });

  const handleCSVUpload = async () => {
    if (csvFile) {
      await mutateAsync({
        csvFile,
        isPPCAudit,
        campaignData,
        userId: imitatedClient.id,
      });
      setCsvFile(null);
    }
  };

  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject } =
    useDropzone({
      maxFiles: 1,
      multiple: false,
      accept: {
        'text/csv': ['.csv'],
      },
      onDrop: async (files) => {
        setCsvFile(files[0]);
      },
    });

  const style = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject, isDarkMode],
  );

  return (
    <Container fixed>
      <Stack
        minHeight={'90vh'}
        width={1}
        margin={'0 auto'}
        paddingY={3}
        spacing={4}
      >
        <Paper 
          elevation={isDarkMode ? 3 : 1} 
          sx={{
            padding: 3,
            backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
            color: isDarkMode ? '#e2e8f0' : '#333333',
            borderRadius: 2,
          }}
        >
          <Stack direction="column" spacing={3} alignItems={'start'}>
            <Typography 
              variant={'h4'} 
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#ffffff' : '#1a202c',
                marginBottom: 1,
              }}
            >
              Instructions:
            </Typography>
            <Box sx={{ 
              '& .MuiTypography-body1': {
                marginBottom: 1,
                color: isDarkMode ? '#cbd5e0' : '#4a5568',
                fontSize: '1rem',
              }
            }}>
              <Typography variant={'body1'}>
                1. Drag and drop/upload your CSV with prospects in the section
                given below
              </Typography>
              <Typography variant={'body1'}>
                2. Make sure you have the necessary fields to generate
                personalised lines
              </Typography>
              <Typography variant={'body1'}>
                3. Leave the screen and come back in a few minutes
              </Typography>
              <Typography variant={'body1'}>
                4. Press download and review the output
              </Typography>
            </Box>
          </Stack>
        </Paper>

        <div>
          {csvFile ? (
            <Stack 
              direction="row" 
              spacing={2} 
              alignItems="center" 
              sx={{
                padding: 2,
                backgroundColor: isDarkMode ? '#2D3748' : '#f7fafc',
                borderRadius: 1,
                border: `1px solid ${isDarkMode ? '#4a5568' : '#e2e8f0'}`,
              }}
            >
              <Typography 
                variant={'body1'} 
                sx={{ 
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  fontWeight: 500,
                }}
              >
                {csvFile.path}
              </Typography>
              <Button
                size={'small'}
                variant={'outlined'}
                sx={{
                  paddingX: 1.5,
                  paddingY: 0.5,
                  borderColor: isDarkMode ? '#718096' : '#cbd5e0',
                  color: isDarkMode ? '#a0aec0' : '#4a5568',
                  '&:hover': {
                    borderColor: isDarkMode ? '#a0aec0' : '#718096',
                    backgroundColor: isDarkMode ? 'rgba(160, 174, 192, 0.08)' : 'rgba(113, 128, 150, 0.08)',
                  }
                }}
                onClick={() => setCsvFile(null)}
              >
                Clear
              </Button>
            </Stack>
          ) : (
            <div {...getRootProps({ style })}>
              <input {...getInputProps()} />
              <Typography 
                variant={'body1'} 
                textAlign="center" 
                sx={{ 
                  fontWeight: 500,
                  fontSize: '1.05rem',
                }}
              >
                Drag and drop CSV file here, or click to select your CSV file
              </Typography>
            </div>
          )}
        </div>

        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'row',
          alignItems: 'center', 
          gap: 2,
          width: '100%',
          paddingY: 2 
        }}>
          <FormControl
            sx={{ 
              flex: 1,
              minWidth: 120, 
              '& .MuiOutlinedInput-root': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                '& fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.palette.primary.main,
                },
              },
              '& .MuiInputLabel-root': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
              },
              '& .MuiSelect-icon': {
                color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.54)',
              },
            }}
            size="small"
          >
            <InputLabel id="campaign" style={{ padding: '2px' }}>
              Campaign
            </InputLabel>
            <Select
              labelId="campaign"
              id="campaign"
              name="campaign"
              label="campaign"
              value={campaignData.campaign}
              size="small"
              MenuProps={{
                PaperProps: {
                  sx: {
                    bgcolor: isDarkMode ? '#2D3748' : '#ffffff',
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& .MuiMenuItem-root': {
                      '&:hover': {
                        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                      },
                      '&.Mui-selected': {
                        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.16)' : 'rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                          backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.24)' : 'rgba(0, 0, 0, 0.12)',
                        },
                      },
                    },
                  },
                },
              }}
            >
              {campaigns &&
                campaigns.length > 0 &&
                campaigns.map((campaign, idx) => (
                  <MenuItem
                    key={idx}
                    onClick={() => {
                      setCampaignData({ ...campaign });
                    }}
                    value={campaign.campaign}
                  >
                    {campaign.campaign}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Checkbox
                value={isPPCAudit}
                onChange={(e) => {
                  e.stopPropagation();
                  setIsPPCAudit((prev) => !prev);
                }}
                sx={{
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : undefined,
                  '&.Mui-checked': {
                    color: isDarkMode ? theme.palette.primary.light : undefined,
                  },
                }}
              />
            }
            label="PPC Audit"
            sx={{
              '& .MuiFormControlLabel-label': {
                color: isDarkMode ? '#e2e8f0' : '#4a5568',
                fontWeight: 500,
              },
              marginRight: 2,
            }}
          />
          
          <Button
            type="submit"
            variant="contained"
            disabled={isPending || !csvFile || !campaignData.campaign}
            size="small"
            onClick={handleCSVUpload}
            sx={{ 
              minWidth: '120px',
              height: '40px',
              backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
              '&:hover': {
                backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8'
              },
              '&.Mui-disabled': {
                backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                color: isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.26)'
              }
            }}
          >
            {isPending ? 'Loading...' : 'Upload'}
          </Button>
        </Box>

        {selectedIds.length > 0 && (
          <Box display="flex" justifyContent="flex-end">
            <GenerateGoogleSheetButton inputArray={selectedIds} />
          </Box>
        )}

        <JobTable
          jobs={jobs}
          type="batch"
          selectedIds={selectedIds}
          setSelectedIds={setSelectedIds}
          isLoading={isLoading}
        />
      </Stack>
    </Container>
  );
};

export default BatchJobDashboard;
