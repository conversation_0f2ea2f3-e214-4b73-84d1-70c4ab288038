/**
 * Example demonstrating UUID validation for PostHog tracking
 * 
 * This example shows how the new UUID validation prevents tracking
 * when UUID8 is detected in searchParams instead of the expected UUID5.
 */

// Import the validation functions
import { isValidUUID5, shouldAllowTracking, trackEvent, trackAuditPageView } from './src/utils/posthog.js';

// Example UUIDs
const uuid5Example = '550e8400-e29b-51d4-a716-************'; // Version 5 (allowed)
const uuid8Example = '550e8400-e29b-81d4-a716-************'; // Version 8 (blocked)
const uuid4Example = '550e8400-e29b-41d4-a716-************'; // Version 4 (allowed)

console.log('=== UUID Validation Examples ===\n');

// Test individual UUID validation
console.log('1. Individual UUID Validation:');
console.log(`UUID5 (${uuid5Example}): ${isValidUUID5(uuid5Example) ? 'ALLOWED' : 'BLOCKED'}`);
console.log(`UUID8 (${uuid8Example}): ${isValidUUID5(uuid8Example) ? 'ALLOWED' : 'BLOCKED'}`);
console.log(`UUID4 (${uuid4Example}): ${isValidUUID5(uuid4Example) ? 'ALLOWED' : 'BLOCKED'}`);
console.log(`Non-UUID string: ${isValidUUID5('campaign-123') ? 'ALLOWED' : 'BLOCKED'}`);
console.log(`Empty string: ${isValidUUID5('') ? 'ALLOWED' : 'BLOCKED'}\n`);

// Test URL parameter validation
console.log('2. URL Parameter Validation:');

// Simulate different URL scenarios
const scenarios = [
  { name: 'No UUID parameters', params: { source: 'web', medium: 'email' } },
  { name: 'Valid UUID5 in uuid param', params: { uuid: uuid5Example } },
  { name: 'UUID8 in uuid param (BLOCKED)', params: { uuid: uuid8Example } },
  { name: 'UUID8 in utm_uuid param (BLOCKED)', params: { utm_uuid: uuid8Example } },
  { name: 'UUID8 in n_uuid param (BLOCKED)', params: { n_uuid: uuid8Example } },
  { name: 'UUID8 in campaign_id param (BLOCKED)', params: { campaign_id: uuid8Example } },
  { name: 'Valid UUID4 in campaignId param', params: { campaignId: uuid4Example } },
];

scenarios.forEach(scenario => {
  const allowed = shouldAllowTracking(scenario.params);
  console.log(`${scenario.name}: ${allowed ? 'TRACKING ALLOWED' : 'TRACKING BLOCKED'}`);
});

console.log('\n=== Tracking Function Behavior ===\n');

// Example of how tracking functions now behave
console.log('3. Tracking Function Examples:');

// Mock window.location for demonstration
if (typeof window !== 'undefined') {
  // Scenario 1: Valid UUID5 - tracking proceeds
  window.location.search = `?uuid=${uuid5Example}`;
  console.log('Scenario 1: Valid UUID5 in URL');
  console.log('- trackEvent will proceed normally');
  console.log('- trackAuditPageView will proceed normally');
  
  // Scenario 2: UUID8 detected - tracking blocked
  window.location.search = `?uuid=${uuid8Example}`;
  console.log('\nScenario 2: UUID8 in URL');
  console.log('- trackEvent will be blocked and log: "Event tracking skipped due to UUID8 detection"');
  console.log('- trackAuditPageView will be blocked and log: "Audit page view tracking skipped due to UUID8 detection"');
}

console.log('\n=== Integration Guide ===\n');

console.log('4. How to Use:');
console.log('The validation is now automatically integrated into all tracking functions:');
console.log('');
console.log('• trackEvent() - Automatically checks searchParams and event properties for UUID8');
console.log('• trackAuditPageView() - Checks searchParams and campaign_id for UUID8');
console.log('• trackAuditInteraction() - Checks searchParams and campaign_id for UUID8');
console.log('');
console.log('No changes needed to existing code - the validation happens automatically!');
console.log('');
console.log('When UUID8 is detected:');
console.log('- Tracking is silently skipped');
console.log('- Console logs explain why tracking was blocked');
console.log('- No errors are thrown');
console.log('- Application continues to function normally');

console.log('\n=== Console Output Examples ===\n');

console.log('5. Expected Console Messages:');
console.log('');
console.log('When UUID8 is detected, you will see logs like:');
console.log('• "UUID8 detected - skipping tracking: 550e8400-e29b-81d4-a716-************"');
console.log('• "Tracking blocked due to UUID8 in parameter \'uuid\': 550e8400-e29b-81d4-a716-************"');
console.log('• "Audit page view tracking skipped due to UUID8 detection"');
console.log('• "Event tracking skipped due to UUID8 detection: custom_event_name"');
console.log('');
console.log('When valid UUIDs are detected:');
console.log('• "Valid UUID5 detected: 550e8400-e29b-51d4-a716-************"');
console.log('• "UUID version 4 detected - allowing tracking: 550e8400-e29b-41d4-a716-************"');
