'use client';

import React, { useState } from 'react';

import {
  Box,
  Button,
  Checkbox,
  Container,
  FormGroup,
  FormControlLabel,
  Grid,
  Stack,
  TextField,
  Typography,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
} from '@mui/material';

import JobTable from './JobTable';

import getAxiosInstance from '../../config/axios';
import { API_ENDPOINTS, getJeffBaseURL } from '../../config/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const fetchUploadCSV = async (data) => {
  const resonse = await getAxiosInstance({
    baseUrl: getJeffBaseURL(),
    cookiesKey: 'jeff-authorization',
  }).post(`${API_ENDPOINTS.JOBS}/${data.userId}`, {
    companyName: data.companyName,
    ...(data.campaignId && { campaignId: data.campaignId }),
    ...(data.productUrl && { productUrl: data.productUrl }),
    ...(data.storeFrontLink && { storeFrontLink: data.storeFrontLink }),
    ...(data.competitorUrl && { competitorUrl: data.competitorUrl }),
    ppcAudit: data.isPPCAudit,
  });

  return resonse.data;
};

const SingleJobDashboard = ({
  imitatedClient,
  jobs,
  setSnackBar,
  campaigns,
  isLoading
}) => {
  const queryClient = useQueryClient();

  const [singleJobQueryData, setSingleJobQueryData] = useState({
    companyName: '',
    productUrl: '',
    storeFrontLink: '',
    competitorUrl: '',
    isPPCAudit: false,
  });
  const [campaignData, setCampaignData] = useState({ id: '', campaign: '' });

  const { mutateAsync, isPending } = useMutation({
    mutationFn: fetchUploadCSV,
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: ['jeff-jobs', { clientId: imitatedClient.id }],
      }),
  });

  const handleGetJobs = async () => {
    if (singleJobQueryData) {
      if (
        singleJobQueryData.companyName &&
        (singleJobQueryData.productUrl || singleJobQueryData.storeFrontLink)
      ) {
        await mutateAsync({
          userId: imitatedClient.id,
          campaignId: campaignData.id,
          ...singleJobQueryData,
        });

        setSingleJobQueryData({
          companyName: '',
          productUrl: '',
          storeFrontLink: '',
          competitorUrl: '',
          isPPCAudit: false,
        });
      } else {
        setSnackBar({
          isOpen: 'true',
          message:
            'Please provide Company Name and either Product URL or Storefront URL.',
          severity: 'error',
        });
        return;
      }
    }
  };

  return (
    <Container fixed>
      <Stack
        minHeight={'90vh'}
        width={1}
        margin={'0 auto'}
        paddingY={3}
        spacing={4}
      >
        <Box
          container
          direction="column"
          padding={2}
          component="form"
          // onSubmit={(e) => e.preventDefault()}
          // noValidate
          sx={{ mt: 1 }}
          spacing={2}
          alignItems="flex-start"
          // justifyContent="center"
        >
          <Grid
            container
            spacing={2}
            alignItems="flex-start"
            // justifyContent="center"
          >
            <Grid item xs={4}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="companyName"
                name="companyName"
                label="Company Name"
                autoComplete="companyName"
                autoFocus
                size="small"
                value={singleJobQueryData.companyName}
                onChange={(e) =>
                  setSingleJobQueryData({
                    ...singleJobQueryData,
                    companyName: e.target.value,
                  })
                }
              />
            </Grid>
          </Grid>

          <Grid
            container
            spacing={1}
            alignItems="center"
            // justifyContent={'center'}
          >
            <Grid item xs={4}>
              <TextField
                margin="normal"
                fullWidth
                id="productUrl"
                name="productUrl"
                label="Product URL"
                autoComplete="productUrl"
                size="small"
                value={singleJobQueryData.productUrl}
                onChange={(e) =>
                  setSingleJobQueryData({
                    ...singleJobQueryData,
                    productUrl: e.target.value,
                  })
                }
              />
            </Grid>

            <Grid item xs={1}>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ paddingLeft: 3 }}
              >
                (or)
              </Typography>
            </Grid>

            <Grid item xs={4}>
              <TextField
                margin="normal"
                fullWidth
                id="storeFrontLink"
                name="storeFrontLink"
                label="Storefront URL"
                autoComplete="storeFrontLink"
                size="small"
                value={singleJobQueryData.storeFrontLink}
                onChange={(e) =>
                  setSingleJobQueryData({
                    ...singleJobQueryData,
                    storeFrontLink: e.target.value,
                  })
                }
              />
            </Grid>
          </Grid>

          {(singleJobQueryData.productUrl ||
            singleJobQueryData.storeFrontLink) && (
            <Grid
              container
              spacing={2}
              alignItems="flex-start"
              // justifyContent="center"
            >
              <Grid item xs={4}>
                <TextField
                  margin="normal"
                  fullWidth
                  id="competitorUrl"
                  name="competitorUrl"
                  label="Competitor URL (Optional)"
                  autoComplete="competitorUrl"
                  size="small"
                  value={singleJobQueryData.competitorUrl}
                  onChange={(e) =>
                    setSingleJobQueryData({
                      ...singleJobQueryData,
                      competitorUrl: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          )}

          <Grid
            container
            spacing={2}
            alignItems="flex-start"
            // justifyContent="center"
          >
            <Grid item xs={4}>
              <FormControl
                sx={{ minWidth: 120, maxWidth: '550px' }}
                size="small"
                fullWidth
              >
                <InputLabel id="campaign" style={{ padding: '2px' }}>
                  Campaign
                </InputLabel>
                <Select
                  labelId="campaign"
                  id="campaign"
                  name="campaign"
                  label="campaign"
                  value={campaignData.campaign}
                  size="small"
                >
                  {campaigns &&
                    campaigns.length > 0 &&
                    campaigns.map((campaign, idx) => (
                      <MenuItem
                        key={idx}
                        onClick={() => {
                          setCampaignData({ ...campaign });
                        }}
                        value={campaign.campaign}
                      >
                        {campaign.campaign}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Grid
            container
            spacing={2}
            alignItems="flex-start"
            // justifyContent="center"
          >
            <Grid item xs={4}>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Checkbox
                      value={singleJobQueryData.isPPCAudit}
                      checked={singleJobQueryData.isPPCAudit}
                      onChange={() =>
                        setSingleJobQueryData((prev) => ({
                          ...prev,
                          isPPCAudit: !prev.isPPCAudit,
                        }))
                      }
                    />
                  }
                  label="PPC Audit"
                />
              </FormGroup>
            </Grid>
          </Grid>
        </Box>

        <Box display="flex" justifyContent="flex-end">
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2, maxWidth: '250px' }}
            disabled={isPending}
            size="small"
            onClick={handleGetJobs}
          >
            {isPending ? 'Loading...' : 'Get Jobs'}
          </Button>
        </Box>

        <JobTable
          jobs={jobs}
          type="single"
          isLoading={isLoading}
        />
      </Stack>
    </Container>
  );
};

export default SingleJobDashboard;
