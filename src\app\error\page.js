'use client';

import React, { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { trackEvent, initPostHog } from '../../utils/posthog';

export default function ErrorPage() {
  const searchParams = useSearchParams();

  useEffect(() => {
    // Initialize PostHog if needed
    initPostHog();

    // Capture all URL information
    const urlInfo = {
      current_url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      referrer_url: typeof window !== 'undefined' ? document.referrer : 'unknown',
      search_params: typeof window !== 'undefined' ? window.location.search : 'unknown',
      pathname: typeof window !== 'undefined' ? window.location.pathname : 'unknown',
      hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
    };

    // Extract all the parameters that would normally be sent to the API
    const originalParams = {
      n_uuid: searchParams.get('n_uuid'),
      utm_uuid: searchParams.get('utm_uuid'),
      utm_content: searchParams.get('utm_content'),
      utm_medium: searchParams.get('utm_medium'),
      utm_campaign: searchParams.get('utm_campaign'),
      utm_source: searchParams.get('utm_source'),
      utm_email: searchParams.get('utm_email'),
      utm_sellerId: searchParams.get('utm_sellerId'),
      utm_term: searchParams.get('utm_term'),
    };

    // Capture ALL search parameters (not just the known ones)
    const allSearchParams = {};
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      for (const [key, value] of urlParams.entries()) {
        allSearchParams[key] = value;
      }
    }

    // Create error tracking data using the same structure as successful redirects
    const errorTrackingData = {
      // Error-specific fields
      error_type: searchParams.get('type') || 'unknown',
      error_source: searchParams.get('source') || 'unknown',
      error_message: searchParams.get('message') || 'No message provided',
      
      // URL tracking fields (make these prominent)
      current_url: urlInfo.current_url,
      referrer_url: urlInfo.referrer_url,
      original_redirect_url: urlInfo.referrer_url, // This should show where they came from
      
      // Use the same field names as successful redirect tracking
      uuid: originalParams.utm_uuid || originalParams.n_uuid || 'unknown',
      client_name: 'error_case', // Since we don't have API response
      type: originalParams.utm_medium || 'unknown',
      campaign: originalParams.utm_campaign || 'unknown',
      seller_id: originalParams.utm_sellerId || 'unknown',
      source: originalParams.utm_source || 'unknown',
      email: originalParams.utm_email || 'unknown',
      redirect_url: null, // No redirect URL in error case
      
      // Original parameters for debugging
      n_uuid: originalParams.n_uuid,
      utm_content: originalParams.utm_content,
      utm_term: originalParams.utm_term,
      
      // All search parameters as JSON for complete debugging
      all_search_params: JSON.stringify(allSearchParams),
      url_search_string: urlInfo.search_params,
      url_pathname: urlInfo.pathname,
      url_hostname: urlInfo.hostname,
      
      // Additional context
      user_agent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
      timestamp: new Date().toISOString(),
    };

    // Track error using the same PostHog utility as other events with descriptive name
    trackEvent('audit_redirect_error', errorTrackingData);
    
    // Also log to console for immediate debugging
    console.error('Redirect Error Details:', errorTrackingData);
    console.error('URL Info:', urlInfo);
    console.error('All Search Params:', allSearchParams);
  }, [searchParams]);

  const errorType = searchParams.get('type') || 'Unknown Error';
  const errorMessage = searchParams.get('message') || 'An error occurred during redirect';

  return (
    <html>
      <head>
        <title>Redirect Error - Equal Collective</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body style={{ 
        fontFamily: 'Arial, sans-serif', 
        margin: 0, 
        padding: '40px 20px',
        backgroundColor: '#f5f5f5',
        color: '#333'
      }}>
        <div style={{
          maxWidth: '600px',
          margin: '0 auto',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{
            color: '#e74c3c',
            marginBottom: '20px',
            fontSize: '28px'
          }}>
            Redirect Error
          </h1>
          
          <div style={{
            backgroundColor: '#fff5f5',
            border: '1px solid #fed7d7',
            borderRadius: '6px',
            padding: '16px',
            marginBottom: '24px'
          }}>
            <h2 style={{
              color: '#c53030',
              margin: '0 0 8px 0',
              fontSize: '18px'
            }}>
              Error Type: {errorType}
            </h2>
            <p style={{
              margin: '0',
              color: '#744c4c'
            }}>
              {errorMessage}
            </p>
          </div>

          <p style={{
            lineHeight: '1.6',
            marginBottom: '24px'
          }}>
            We apologize for the inconvenience. The redirect link you followed encountered an error. 
            Our team has been automatically notified and will investigate this issue.
          </p>

          <div style={{
            backgroundColor: '#f7fafc',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '16px',
            marginBottom: '24px'
          }}>
            <h3 style={{
              margin: '0 0 12px 0',
              fontSize: '16px',
              color: '#2d3748'
            }}>
              What you can do:
            </h3>
            <ul style={{
              margin: '0',
              paddingLeft: '20px',
              lineHeight: '1.6'
            }}>
              <li>Check if the link was copied correctly</li>
              <li>Try refreshing the page</li>
              <li>Contact support if the issue persists</li>
            </ul>
          </div>

          <div style={{
            textAlign: 'center',
            paddingTop: '20px',
            borderTop: '1px solid #e2e8f0'
          }}>
            <a 
              href="https://equalcollective.com" 
              style={{
                backgroundColor: '#3182ce',
                color: 'white',
                padding: '12px 24px',
                textDecoration: 'none',
                borderRadius: '6px',
                display: 'inline-block',
                fontSize: '16px'
              }}
            >
              Go to Equal Collective
            </a>
          </div>
        </div>
      </body>
    </html>
  );
} 