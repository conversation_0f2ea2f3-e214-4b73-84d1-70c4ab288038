'use client'; // This is a client component 👈🏽

import React, { useState } from 'react';
import { alpha, styled } from '@mui/material/styles';
import DownloadIcon from '@mui/icons-material/Download';
import IconButton from '@mui/material/IconButton';
import Checkbox from '@mui/material/Checkbox';
import {
  Typography,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Link,
  Tooltip,
  Box,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import { format } from 'date-fns';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import InfoIcon from '@mui/icons-material/Info';

import getAxiosInstance from '../../config/axios';
import { API_ENDPOINTS, getJeffBaseURL } from '../../config/api';
import { useAdminTheme } from './AdminThemeContext';
import { light } from '../../theme/palette';
import QaStatsModal from './Utils/qaStatsModal';

const StyledTableCell = styled(TableCell)(({ isDarkMode }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: isDarkMode ? '#2D3748' : light.alternate.main,
    color: isDarkMode ? '#E2E8F0' : light.text.primary,
    fontWeight: 600,
    padding: '16px',
    whiteSpace: 'nowrap',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
    color: isDarkMode ? '#E2E8F0' : light.text.primary,
    padding: '12px 16px',
    '&.mail-data': {
      maxWidth: '300px',
      overflow: 'hidden',
    },
  },
}));

const StyledTableRow = styled(TableRow)(({ isDarkMode, isSelected }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: isDarkMode ? alpha('#000000', 0.15) : light.alternate.main,
  },
  '&:hover': {
    backgroundColor: isDarkMode
      ? 'rgba(255, 255, 255, 0.05)'
      : alpha(light.primary.main, 0.04),
  },
  backgroundColor: isSelected
    ? isDarkMode
      ? 'rgba(44, 82, 130, 0.2)'
      : alpha(light.primary.main, 0.08)
    : 'transparent',
  // hide last border
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

const DownloadButton = styled(IconButton)(({ isDarkMode }) => ({
  color: isDarkMode ? '#90CAF9' : light.primary.main,
  backgroundColor: isDarkMode
    ? 'rgba(144, 202, 249, 0.1)'
    : alpha(light.primary.main, 0.04),
  padding: '8px',
  border: `1px solid ${
    isDarkMode ? 'rgba(144, 202, 249, 0.2)' : alpha(light.primary.main, 0.1)
  }`,
  borderRadius: '4px',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: isDarkMode
      ? 'rgba(144, 202, 249, 0.2)'
      : alpha(light.primary.main, 0.08),
    border: `1px solid ${
      isDarkMode ? 'rgba(144, 202, 249, 0.3)' : alpha(light.primary.main, 0.2)
    }`,
  },
  '& .MuiSvgIcon-root': {
    fontSize: '1.25rem',
  },
}));

const QaStatsButton = styled(IconButton)(({ isDarkMode }) => ({
  color: isDarkMode ? '#90CAF9' : light.primary.main,
  backgroundColor: isDarkMode
    ? 'rgba(144, 202, 249, 0.1)'
    : alpha(light.primary.main, 0.04),
  padding: '8px',
  border: `1px solid ${
    isDarkMode ? 'rgba(144, 202, 249, 0.2)' : alpha(light.primary.main, 0.1)
  }`,
  borderRadius: '4px',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: isDarkMode
      ? 'rgba(144, 202, 249, 0.2)'
      : alpha(light.primary.main, 0.08),
    border: `1px solid ${
      isDarkMode ? 'rgba(144, 202, 249, 0.3)' : alpha(light.primary.main, 0.2)
    }`,
  },
  '& .MuiSvgIcon-root': {
    fontSize: '1.25rem',
  },
}));

const MailDataCell = ({ data, isDarkMode }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDialog, setShowDialog] = useState(false);

  const truncatedData =
    data?.length > 150 ? `${data.substring(0, 150)}...` : data;

  return (
    <StyledTableCell align="left" isDarkMode={isDarkMode} className="mail-data">
      <Box sx={{ position: 'relative' }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: 1,
            cursor: data?.length > 150 ? 'pointer' : 'default',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              whiteSpace: isExpanded ? 'normal' : 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: '100%',
              transition: 'all 0.2s ease',
            }}
            onClick={() => data?.length > 150 && setShowDialog(true)}
          >
            {isExpanded ? data : truncatedData || '-'}
          </Typography>
          {data?.length > 150 && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
              sx={{
                padding: 0,
                color: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: 'transparent',
                  color: isDarkMode ? '#63B3ED' : '#1565c0',
                },
              }}
            >
              {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>
      </Box>

      <Dialog
        open={showDialog}
        onClose={() => setShowDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
            color: isDarkMode ? '#E2E8F0' : '#000000',
          },
        }}
      >
        <DialogTitle
          sx={{
            borderBottom: 1,
            borderColor: isDarkMode
              ? 'rgba(255, 255, 255, 0.12)'
              : 'rgba(0, 0, 0, 0.12)',
            color: isDarkMode ? '#E2E8F0' : '#000000',
          }}
        >
          Mail Data Content
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Typography
            sx={{
              whiteSpace: 'pre-wrap',
              color: isDarkMode ? '#E2E8F0' : '#000000',
            }}
          >
            {data}
          </Typography>
        </DialogContent>
        <DialogActions
          sx={{
            borderTop: 1,
            borderColor: isDarkMode
              ? 'rgba(255, 255, 255, 0.12)'
              : 'rgba(0, 0, 0, 0.12)',
            p: 2,
          }}
        >
          <Button
            onClick={() => setShowDialog(false)}
            variant="contained"
            sx={{
              bgcolor: isDarkMode ? '#4299E1' : '#1976d2',
              '&:hover': {
                bgcolor: isDarkMode ? '#63B3ED' : '#1565c0',
              },
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </StyledTableCell>
  );
};

const JobTable = ({
  jobs = [],
  type,
  selectedIds = [],
  setSelectedIds,
  isLoading,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  // State to manage modal visibility and response data
  const [qaStatsModalOpen, setQaStatsModalOpen] = useState(false);
  const [qaStatsData, setQaStatsData] = useState(null);

  const handleCheckboxChange = (id) => {
    if (!selectedIds) return;
    if (selectedIds.includes(id)) {
      setSelectedIds(selectedIds.filter((selectedId) => selectedId !== id));
    } else {
      setSelectedIds([...selectedIds, id]);
    }
  };

  const getStatusElement = (status) => {
    const statusInfo = {
      completed: {
        color: isDarkMode ? '#68D391' : '#48BB78',
        bgColor: isDarkMode
          ? 'rgba(104, 211, 145, 0.15)'
          : alpha('#48BB78', 0.1),
      },
      failed: {
        color: isDarkMode ? '#FC8181' : '#E53E3E',
        bgColor: isDarkMode
          ? 'rgba(252, 129, 129, 0.15)'
          : alpha('#E53E3E', 0.1),
      },
      default: {
        color: isDarkMode ? '#F6E05E' : light.secondary.main,
        bgColor: isDarkMode
          ? 'rgba(246, 224, 94, 0.15)'
          : alpha(light.secondary.main, 0.1),
      },
    };

    const style = statusInfo[status] || statusInfo.default;

    return (
      <Typography
        sx={{
          display: 'inline-block',
          px: 1.5,
          py: 0.5,
          borderRadius: 1,
          fontSize: '0.8rem',
          fontWeight: 500,
          backgroundColor: style.bgColor,
          color: style.color,
        }}
      >
        {status}
      </Typography>
    );
  };

  const handleAnalyzableCsvFile = async (id, slug) => {
    const response = await getAxiosInstance({
      baseUrl: getJeffBaseURL(),
      cookiesKey: 'jeff-authorization',
    }).get(`${API_ENDPOINTS.JEFF_ANALYZABLE_CSV_DOWNLOAD}/${id}/${slug}`, {
      responseType: 'blob',
    });

    const contentDisposition = response.headers['content-disposition'];
    const filenameMatch =
      contentDisposition &&
      contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    const filename =
      filenameMatch && filenameMatch[1]
        ? filenameMatch[1].replace(/['"]/g, '')
        : 'report22.csv';

    const url = window.URL.createObjectURL(new Blob([response.data]));
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    return await response.data;
  };

  const handleQaStatsButton = async (id) => {
    const response = await getAxiosInstance({
      baseUrl: getJeffBaseURL(),
      cookiesKey: 'jeff-authorization',
    }).get(`${API_ENDPOINTS.QA_STATS}/${id}`, {
      responseType: 'json',
    });

    console.log('Response From Stats:', response.data);
    setQaStatsData(response.data); // Save the response data to state
    setQaStatsModalOpen(true); // Open the modal
  };


  return (
    <>
      <TableContainer
        component={Paper}
        sx={{
          backgroundColor: isDarkMode ? '#1A202C' : light.background.paper,
          boxShadow: isDarkMode
            ? '0 4px 6px rgba(0, 0, 0, 0.25)'
            : `0 1px 3px ${light.cardShadow}`,
          borderRadius: 1,
          overflow: 'hidden',
          overflowX: 'auto',
        }}
      >
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress
              sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }}
            />
          </Box>
        ) : (
          <Table sx={{ minWidth: 800 }} aria-label="customized table">
            <TableHead>
              <TableRow>
                {type === 'batch' && (
                  <>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Select
                    </StyledTableCell>
                    <StyledTableCell align="left" isDarkMode={isDarkMode}>
                      File Name
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Created At
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      CSV Status
                    </StyledTableCell>
                    <StyledTableCell align="left" isDarkMode={isDarkMode}>
                      Campaign
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Qualified Leads
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Total Leads
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Successful Data
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Non-Revenue Data
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Unsuccessful Data
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      QA Stats
                    </StyledTableCell>
                  </>
                )}
                {type === 'single' && (
                  <>
                    <StyledTableCell align="left" isDarkMode={isDarkMode}>
                      Name
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Created At
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Status
                    </StyledTableCell>
                    <StyledTableCell align="left" isDarkMode={isDarkMode}>
                      Mail Data
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      Page Link
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      PDF Link
                    </StyledTableCell>
                    <StyledTableCell align="center" isDarkMode={isDarkMode}>
                      QA Stats
                    </StyledTableCell>
                  </>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {jobs &&
                jobs
                  .sort((a, b) => b.id - a.id)
                  .map((row) => {
                    const isAllStatusCompleted = row.csvStatus === 'completed';
                    const result = format(new Date(row.createdAt), 'Pp');
                    const isSelected = selectedIds.includes(row.id);

                    return (
                      <StyledTableRow
                        key={row.id}
                        isDarkMode={isDarkMode}
                        isSelected={isSelected}
                        sx={{
                          transition: 'background-color 0.2s ease',
                        }}
                      >
                        {type === 'batch' && (
                          <StyledTableCell
                            align="center"
                            isDarkMode={isDarkMode}
                          >
                            <Checkbox
                              checked={isSelected}
                              onChange={() => handleCheckboxChange(row.id)}
                              sx={{
                                color: isDarkMode
                                  ? 'rgba(255, 255, 255, 0.5)'
                                  : alpha(light.text.primary, 0.4),
                                '&.Mui-checked': {
                                  color: isDarkMode
                                    ? '#90CAF9'
                                    : light.primary.main,
                                },
                              }}
                            />
                          </StyledTableCell>
                        )}
                        {type === 'batch' && (
                          <>
                            <StyledTableCell
                              align="left"
                              isDarkMode={isDarkMode}
                            >
                              {row.fileName}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {result}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {getStatusElement(row.csvStatus)}
                            </StyledTableCell>
                            <StyledTableCell
                              align="left"
                              isDarkMode={isDarkMode}
                            >
                              {row.campaignName || '-'}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {row.qualifiedLeads || '-'}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {row.totalLeads || '-'}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {isAllStatusCompleted ? (
                                <Tooltip title="Download successful data with revenue">
                                  <DownloadButton
                                    onClick={() =>
                                      handleAnalyzableCsvFile(
                                        row.id,
                                        'success-with-revenue',
                                      )
                                    }
                                    isDarkMode={isDarkMode}
                                    size="small"
                                    aria-label="download successful data with revenue"
                                  >
                                    <DownloadIcon />
                                  </DownloadButton>
                                </Tooltip>
                              ) : (
                                '-'
                              )}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {isAllStatusCompleted ? (
                                <Tooltip title="Download successful data without revenue">
                                  <DownloadButton
                                    onClick={() =>
                                      handleAnalyzableCsvFile(
                                        row.id,
                                        'success-without-revenue',
                                      )
                                    }
                                    isDarkMode={isDarkMode}
                                    size="small"
                                    aria-label="download successful data without revenue"
                                  >
                                    <DownloadIcon />
                                  </DownloadButton>
                                </Tooltip>
                              ) : (
                                '-'
                              )}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {isAllStatusCompleted &&
                              row.totalLeads !== row.qualifiedLeads ? (
                                  <Tooltip title="Download unsuccessful data">
                                    <DownloadButton
                                      onClick={() =>
                                        handleAnalyzableCsvFile(
                                          row.id,
                                          'unsuccessful',
                                        )
                                      }
                                      isDarkMode={isDarkMode}
                                      size="small"
                                      aria-label="download unsuccessful data"
                                    >
                                      <DownloadIcon />
                                    </DownloadButton>
                                  </Tooltip>
                                ) : (
                                  '-'
                                )}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {isAllStatusCompleted ? (
                                <Tooltip title="Download successful data without revenue">
                                  <QaStatsButton
                                    onClick={() => handleQaStatsButton(row.id)}
                                    isDarkMode={isDarkMode}
                                    size="small"
                                    aria-label="get qa stats"
                                  >
                                    <InfoIcon />
                                  </QaStatsButton>
                                </Tooltip>
                              ) : (
                                '-'
                              )}
                            </StyledTableCell>
                          </>
                        )}

                        {type === 'single' && (
                          <>
                            <StyledTableCell
                              align="left"
                              isDarkMode={isDarkMode}
                            >
                              {row.fileName}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {row.createdAt
                                ? format(new Date(row.createdAt), 'Pp')
                                : '-'}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {getStatusElement(row.csvStatus)}
                            </StyledTableCell>
                            <MailDataCell
                              data={row.data?.mailData}
                              isDarkMode={isDarkMode}
                            />
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {row.data?.webpageLink ? (
                                <Link
                                  href={row.data.webpageLink}
                                  target="_blank"
                                  sx={{
                                    color: isDarkMode
                                      ? '#63B3ED'
                                      : light.primary.main,
                                    textDecoration: 'none',
                                    '&:hover': {
                                      textDecoration: 'underline',
                                    },
                                  }}
                                >
                                  View Page
                                </Link>
                              ) : (
                                '-'
                              )}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {row.data?.pdfLink ? (
                                <Link
                                  href={row.data.pdfLink}
                                  target="_blank"
                                  sx={{
                                    color: isDarkMode
                                      ? '#63B3ED'
                                      : light.primary.main,
                                    textDecoration: 'none',
                                    '&:hover': {
                                      textDecoration: 'underline',
                                    },
                                  }}
                                >
                                  View PDF
                                </Link>
                              ) : (
                                '-'
                              )}
                            </StyledTableCell>
                            <StyledTableCell
                              align="center"
                              isDarkMode={isDarkMode}
                            >
                              {isAllStatusCompleted ? (
                                <Tooltip title="Download successful data without revenue">
                                  <QaStatsButton
                                    onClick={() => handleQaStatsButton(row.id)}
                                    isDarkMode={isDarkMode}
                                    size="small"
                                    aria-label="download successful data without revenue"
                                  >
                                    <InfoIcon />
                                  </QaStatsButton>
                                </Tooltip>
                              ) : (
                                '-'
                              )}
                            </StyledTableCell>
                          </>
                        )}
                      </StyledTableRow>
                    );
                  })}
              {jobs.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={type === 'batch' ? 10 : 6}
                    align="center"
                    sx={{
                      py: 5,
                      color: isDarkMode ? '#A0AEC0' : light.text.secondary,
                    }}
                  >
                    <Typography variant="body1">No jobs found</Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Upload a CSV file to get started
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </TableContainer>
      <QaStatsModal
        open={qaStatsModalOpen}
        onClose={() => setQaStatsModalOpen(false)}
        data={qaStatsData}
        isDarkMode={isDarkMode}
      />
    </>
  );
};

export default JobTable;
