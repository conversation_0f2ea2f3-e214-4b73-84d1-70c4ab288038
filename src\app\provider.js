'use client';

import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { AdminThemeProvider } from '../views/Jeff/AdminThemeContext';

const Providers = ({ children }) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
          },
        },
      }),
  );

  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_POSTHOG_KEY;
    
    // Only initialize if we have an API key and PostHog isn't already initialized
    if (apiKey && !posthog.__loaded) {
      console.log('Initializing PostHog provider...');
      posthog.init(apiKey, {
        api_host: '/ingest',
        ui_host: 'https://us.posthog.com',
        capture_pageview: 'history_change',
        capture_exceptions: true,
        loaded: (ph) => {
          if (process.env.NODE_ENV === 'development') ph.debug();
        },
        debug: process.env.NODE_ENV === 'development',
      });
    } else if (!apiKey) {
      console.warn('PostHog API key not found in provider');
    }
  }, []);

  return (
    <PostHogProvider client={posthog}>
      <QueryClientProvider client={queryClient}>
        <AdminThemeProvider>
          {children}
          <ReactQueryDevtools initialIsOpen={false} />
        </AdminThemeProvider>
      </QueryClientProvider>
    </PostHogProvider>
  );
};

export default Providers;
