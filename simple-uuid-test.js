/**
 * Simple UUID Test - Works with or without uuid package
 * 
 * This demonstrates UUID generation and validation for PostHog testing.
 * If uuid package is available, it uses the proper library.
 * If not, it shows examples and provides fallback generation.
 * 
 * Run: node simple-uuid-test.js
 */

let uuidv4, uuidv5;
let hasUuidPackage = false;

// Try to import uuid package
try {
  const uuid = require('uuid');
  uuidv4 = uuid.v4;
  uuidv5 = uuid.v5;
  hasUuidPackage = true;
  console.log('✅ UUID package found - using proper UUID generation');
} catch (error) {
  console.log('⚠️  UUID package not found - using fallback generation');
  console.log('   Install with: npm install uuid');
  hasUuidPackage = false;
}

// Fallback UUID generation (for demonstration when package not available)
const crypto = require('crypto');

const fallbackUUID4 = () => {
  return crypto.randomUUID();
};

const fallbackUUID5 = (name, namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341') => {
  // Simple deterministic UUID5-like generation for demo
  const hash = crypto.createHash('sha1');
  hash.update(namespace + name);
  const hex = hash.digest('hex');
  
  // Format as UUID with version 5
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    '5' + hex.substring(13, 16), // Force version 5
    '8' + hex.substring(17, 20), // Force variant
    hex.substring(20, 32)
  ].join('-');
};

// UUID8 simulation (for testing blocking)
const generateUUID8 = (customData) => {
  const hash = crypto.createHash('sha256');
  hash.update(customData, 'utf8');
  const hashBytes = hash.digest();
  
  // Set version (8) and variant bits
  hashBytes[6] = (hashBytes[6] & 0x0f) | 0x80; // Version 8
  hashBytes[8] = (hashBytes[8] & 0x3f) | 0x80; // Variant 10
  
  // Format as UUID string
  const hex = hashBytes.toString('hex');
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    hex.substring(12, 16),
    hex.substring(16, 20),
    hex.substring(20, 32)
  ].join('-');
};

// Choose which functions to use
const generateUUID4 = hasUuidPackage ? uuidv4 : fallbackUUID4;
const generateUUID5 = hasUuidPackage ? 
  (name, namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341') => uuidv5(name, namespace) :
  fallbackUUID5;

// Simple validation function (matches the one in posthog.js)
const isValidUUID5 = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true;
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(uuid)) {
    return true; // Allow non-UUID strings
  }
  
  const version = parseInt(uuid.charAt(14), 16);
  return version !== 8; // Block only UUID8
};

// Run tests
console.log('\n🧪 UUID Generation and Validation Test\n');
console.log('=' .repeat(50));

// Test campaigns
const campaigns = [
  'summer-2024-campaign',
  'winter-holiday-promo',
  'black-friday-sale'
];

console.log('\n📝 Generating Test UUIDs:\n');

// Generate UUID5s (allowed)
console.log('🟢 UUID5 Generation (ALLOWED):');
const uuid5s = campaigns.map((campaign, index) => {
  const uuid = generateUUID5(campaign);
  console.log(`  ${index + 1}. ${campaign}`);
  console.log(`     ${uuid} (v${uuid.charAt(14)})`);
  return { campaign, uuid };
});

// Generate UUID4 (allowed)
console.log('\n🔵 UUID4 Generation (ALLOWED):');
const uuid4 = generateUUID4();
console.log(`  Random: ${uuid4} (v${uuid4.charAt(14)})`);

// Generate UUID8s (blocked)
console.log('\n🔴 UUID8 Generation (BLOCKED):');
const uuid8s = campaigns.map((campaign, index) => {
  const uuid = generateUUID8(campaign);
  console.log(`  ${index + 1}. ${campaign}`);
  console.log(`     ${uuid} (v${uuid.charAt(14)})`);
  return { campaign, uuid };
});

// Test validation
console.log('\n🔍 Validation Results:\n');

console.log('UUID5 Validation (should be ALLOWED):');
uuid5s.forEach(({ campaign, uuid }) => {
  const result = isValidUUID5(uuid);
  console.log(`  ${campaign}: ${result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
});

console.log('\nUUID4 Validation (should be ALLOWED):');
const uuid4Result = isValidUUID5(uuid4);
console.log(`  Random UUID4: ${uuid4Result ? '✅ ALLOWED' : '❌ BLOCKED'}`);

console.log('\nUUID8 Validation (should be BLOCKED):');
uuid8s.forEach(({ campaign, uuid }) => {
  const result = isValidUUID5(uuid);
  console.log(`  ${campaign}: ${result ? '✅ ALLOWED' : '❌ BLOCKED'}`);
});

// URL examples
console.log('\n🌐 Example URLs:\n');

console.log('✅ URLs that ALLOW tracking:');
console.log(`  /audit/product?campaign_id=${uuid5s[0].uuid}`);
console.log(`  /audit/product?uuid=${uuid4}`);
console.log(`  /audit/product?utm_uuid=${uuid5s[1].uuid}`);

console.log('\n❌ URLs that BLOCK tracking:');
console.log(`  /audit/product?campaign_id=${uuid8s[0].uuid}`);
console.log(`  /audit/product?uuid=${uuid8s[1].uuid}`);

// Code examples
console.log('\n💻 Code Examples for Your Tests:\n');

if (hasUuidPackage) {
  console.log('Using the uuid package (RECOMMENDED):');
  console.log(`
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';

// Generate UUID5 (deterministic)
const uuid5 = uuidv5('my-campaign', namespace);
console.log(uuid5); // ${uuid5s[0].uuid}

// Generate UUID4 (random)  
const uuid4 = uuidv4();
console.log(uuid4); // Different every time

// Test validation
import { isValidUUID5 } from './src/utils/posthog';
console.log(isValidUUID5(uuid5)); // true
console.log(isValidUUID5(uuid4)); // true
`);
} else {
  console.log('To use proper UUID generation, install the uuid package:');
  console.log(`
npm install uuid

Then use:
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';
const uuid5 = uuidv5('my-campaign', namespace);
const uuid4 = uuidv4();
`);
}

console.log('\n' + '=' .repeat(50));
console.log('✨ Test complete!');

if (!hasUuidPackage) {
  console.log('\n📦 Next steps:');
  console.log('1. Run: npm install uuid');
  console.log('2. Re-run this test to see proper UUID generation');
  console.log('3. Use the examples above in your Jest tests');
}

// Export for use in other files
module.exports = {
  generateUUID4,
  generateUUID5,
  generateUUID8,
  isValidUUID5,
  hasUuidPackage,
  testData: {
    uuid5s: uuid5s.map(item => item.uuid),
    uuid8s: uuid8s.map(item => item.uuid),
    uuid4
  }
};
