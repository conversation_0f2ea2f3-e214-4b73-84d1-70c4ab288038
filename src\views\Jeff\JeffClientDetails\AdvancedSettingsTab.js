import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Grid,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Card,
  CardContent,
  Typography,
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import SettingsIcon from '@mui/icons-material/Settings';
import { API_ENDPOINTS } from '../../../config/api';
import getAxiosInstance from '../../../config/axios';
import getUserConfigData from 'views/Jeff/Utils/getUserConfigData';

// Configuration field definitions
const jungleScout = {
  apiKey: 'API KEY',
  baseURL: 'Base URL',
  keyName: 'Key Name',
};

const configOpenAi = {
  apiKey: 'API KEY',
  modelId: 'Model ID',
  assistantId: 'Assistant ID',
};

const compEmailTemplates = {
  noRevenue: 'No Revenue',
  haveRevenue: 'Have Revenue',
};

const configPromptTemplates = {
  compSearchKeyword: 'Comp Search Keyword',
  caseStudies: 'Case Studies',
  bsrCategoryMatch: 'BSR Category Match',
  ppcAudit: 'PPC Audit',
  mainImageOptimisation: 'Main Image Optimisation',
  companyNameHumanisation: 'Company Name Humanisation',
  productTitleHumanisation: 'Product Title Humanisation',
};

const scrappedApiData = {
  SCRAPER_API_KEY: 'API Key',
  SCRAPER_API_HOST: 'API Host',
  SCRAPER_API_PORT: 'API Port',
  SCRAPER_API_USERNAME: 'API Username',
  SCRAPER_API_MAX_RETRIES: 'API Max retries',
};

const AdvancedSettingsTab = ({
  clientData,
  isDarkMode,
  fetchData,
  setSnackBar,
  loading,
  setLoading,
}) => {
  const [adminData, setAdminData] = useState({});

  const [singleEntryOpt, setSingleEntryOpt] = useState(
    clientData?.Configurations?.[0]?.singleEntryOpt ??
      clientData?.singleEntryOpt ??
      false,
  );
  const [scraperApiValues, setScraperApiValues] = useState(
    clientData?.Configurations?.[0]?.scraperApi || {},
  );
  const [jungleScoutValues, setJungleScoutValues] = useState(
    clientData?.Configurations?.[0]?.jungleScout || {},
  );
  const [openAiValues, setOpenAiValues] = useState(
    clientData?.Configurations?.[0]?.openAi || {},
  );
  const [compEmailTemplateValues, setCompEmailTemplateValues] = useState({
    completed:
      clientData?.Configurations?.[0]?.compEmailTemplates?.completed || {},
  });
  const [promptTemplateValues, setPromptTemplateValues] = useState(
    clientData?.Configurations?.[0]?.promptTemplates || {},
  );

  const fetchAdminData = async () => {
    setLoading(true);
    try {
      const adminConfigData = await getUserConfigData(1);
      setAdminData(
        adminConfigData?.Configurations && adminConfigData?.Configurations[0],
      );
    } catch (err) {
      console.error('Error fetching client data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAdminData();
  }, []);

  useEffect(() => {
    if (clientData) {
      setSingleEntryOpt(
        clientData?.Configurations?.[0]?.singleEntryOpt ??
          clientData?.singleEntryOpt ??
          false,
      );
      if (clientData.Configurations && clientData.Configurations.length > 0) {
        const config = clientData.Configurations[0];
        setScraperApiValues(config.scraperApi || {});
        setJungleScoutValues(config.jungleScout || {});
        setOpenAiValues(config.openAi || {});
        setCompEmailTemplateValues({
          completed: config.compEmailTemplates?.completed || {},
        });
        setPromptTemplateValues(config.promptTemplates || {});
      }
    }
  }, [clientData]);

  const handleInputChange = (setStateFunction, field, value) => {
    setStateFunction((prevState) => ({
      ...prevState,
      [field]: value,
    }));
  };

  async function postClientConfig(data) {
    const baseUrl = 'https://api.jeff.equalcollective.com';

    try {
      const response = await getAxiosInstance({
        baseUrl: baseUrl,
        cookiesKey: 'jeff-authorization', // Assumes authorization via cookies
      }).post(`${API_ENDPOINTS.CLIENT_CONFIG}`, data);

      return response.data;
    } catch (error) {
      console.error('Error posting client config:', error);
      throw error;
    }
  }

  const handleSave = async () => {
    const updatedData = {
      clientId: clientData.id,
      singleEntryOpt: singleEntryOpt,
      ...(scraperApiValues &&
        Object.keys(configOpenAi).length > 0 && {
        scraperApi: scraperApiValues,
      }),
      ...(jungleScoutValues &&
        Object.keys(jungleScoutValues).length > 0 && {
        jungleScout: jungleScoutValues,
      }),
      ...(openAiValues &&
        Object.keys(openAiValues).length > 0 && { openAi: openAiValues }),
      ...(compEmailTemplateValues.completed &&
        Object.keys(compEmailTemplateValues.completed).length > 0 && {
        compEmailTemplates: compEmailTemplateValues,
      }),
      ...(promptTemplateValues &&
        Object.keys(promptTemplateValues).length > 0 && {
        promptTemplates: promptTemplateValues,
      }),
    };

    try {
      setLoading(true);
      await postClientConfig(updatedData);
      setSnackBar({
        isOpen: true,
        message: 'Configuration saved successfully!',
        severity: 'success',
      });
      fetchData();
    } catch (error) {
      setSnackBar({
        isOpen: true,
        message: error?.response?.data?.error || 'Failed to save configuration',
        severity: 'error',
      });
    } finally {
      setLoading(false); // Show the snackbar after save attempt
    }
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={singleEntryOpt}
                onChange={(e) => setSingleEntryOpt(e.target.checked)}
                sx={{
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&.Mui-checked': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              />
            }
            label="Single Entry Option"
            sx={{
              color: isDarkMode ? '#E2E8F0' : 'inherit',
            }}
          />
        </FormGroup>
      </Grid>
      <Grid item xs={12}>
        {/* API Configuration Card */}
        <Card
          elevation={isDarkMode ? 2 : 1}
          sx={{
            backgroundColor: isDarkMode ? '#1E293B' : '#ffffff',
            color: isDarkMode ? '#E2E8F0' : 'inherit',
            boxShadow: isDarkMode
              ? '0px 3px 15px rgba(0,0,0,0.2)'
              : '0px 2px 8px rgba(0,0,0,0.05)',
            mb: 4,
          }}
        >
          <CardContent>
            <Typography
              variant="h6"
              gutterBottom
              color={isDarkMode ? '#ffffff' : 'text.primary'}
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 2,
              }}
            >
              <SettingsIcon
                sx={{
                  mr: 1,
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                }}
              />
              API Configuration
            </Typography>

            <Typography
              variant="body2"
              color={isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'text.secondary'}
              paragraph
            >
              Configure API settings for integration with various services.
            </Typography>

            {/* Scraper API Configuration */}
            <Typography
              variant="subtitle1"
              gutterBottom
              color={isDarkMode ? '#90CAF9' : '#1976d2'}
              sx={{ mt: 4 }}
            >
              Scraper API Configuration
            </Typography>
            {Object.entries(scrappedApiData).map(([key, label]) => (
              <Box
                key={key}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  mb: 1,
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: '600',
                    color: isDarkMode ? '#e2e8f0' : '#2e2e2e',
                    minWidth: '120px',
                  }}
                >
                  {label}
                </Typography>
                <Box sx={{ marginInline: '6px' }}>-</Box>
                <TextField
                  fullWidth
                  variant="standard"
                  value={scraperApiValues[key] || ''}
                  onChange={(e) =>
                    handleInputChange(setScraperApiValues, key, e.target.value)
                  }
                  sx={{
                    '& .MuiInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                />
                <Button
                  variant="text"
                  color="primary"
                  sx={{ minWidth: '120px', ml: 1 }}
                  onClick={() => {
                    handleInputChange(
                      setScraperApiValues,
                      key,
                      adminData?.scraperApi?.[key] || '',
                    );
                  }}
                >
                  Set Default
                </Button>
              </Box>
            ))}

            {/* JungleScout API Configuration */}
            <Typography
              variant="subtitle1"
              gutterBottom
              color={isDarkMode ? '#90CAF9' : '#1976d2'}
              sx={{ mt: 4 }}
            >
              JungleScout Configuration
            </Typography>
            {Object.entries(jungleScout).map(([key, label]) => (
              <Box
                key={key}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  mb: 1,
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: '600',
                    color: isDarkMode ? '#e2e8f0' : '#2e2e2e',
                    minWidth: '85px',
                  }}
                >
                  {label}
                </Typography>
                <Box sx={{ marginInline: '6px' }}>-</Box>
                <TextField
                  fullWidth
                  variant="standard"
                  value={jungleScoutValues[key] || ''}
                  onChange={(e) =>
                    handleInputChange(setJungleScoutValues, key, e.target.value)
                  }
                  sx={{
                    '& .MuiInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                />
                <Button
                  variant="text"
                  color="primary"
                  sx={{ minWidth: '120px', ml: 1 }}
                  onClick={() => {
                    handleInputChange(
                      setJungleScoutValues,
                      key,
                      adminData?.jungleScout?.[key] || '',
                    );
                  }}
                >
                  Set Default
                </Button>
              </Box>
            ))}

            {/* OpenAI Configuration */}
            <Typography
              variant="subtitle1"
              gutterBottom
              color={isDarkMode ? '#90CAF9' : '#1976d2'}
              sx={{ mt: 4 }}
            >
              OpenAI Configuration
            </Typography>
            {Object.entries(configOpenAi).map(([key, label]) => (
              <Box
                key={key}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  mb: 1,
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: '600',
                    color: isDarkMode ? '#e2e8f0' : '#2e2e2e',
                    minWidth: '100px',
                  }}
                >
                  {label}
                </Typography>
                <Box sx={{ marginInline: '6px' }}>-</Box>
                <TextField
                  fullWidth
                  variant="standard"
                  multiline={key === 'apiKey'}
                  value={openAiValues[key] || ''}
                  onChange={(e) =>
                    handleInputChange(setOpenAiValues, key, e.target.value)
                  }
                  sx={{
                    '& .MuiInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                />
                <Button
                  variant="text"
                  color="primary"
                  sx={{ minWidth: '120px', ml: 1 }}
                  onClick={() => {
                    handleInputChange(
                      setOpenAiValues,
                      key,
                      adminData?.openAi?.[key] || '',
                    );
                  }}
                >
                  Set Default
                </Button>
              </Box>
            ))}

            {/* Comp Email Templates */}
            <Typography
              variant="subtitle1"
              gutterBottom
              color={isDarkMode ? '#90CAF9' : '#1976d2'}
              sx={{ mt: 4 }}
            >
              Comp Email Templates
            </Typography>
            {Object.entries(compEmailTemplates).map(([key, label]) => (
              <Box
                key={key}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  mb: 1,
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: '600',
                    color: isDarkMode ? '#e2e8f0' : '#2e2e2e',
                    minWidth: '120px',
                  }}
                >
                  {label}
                </Typography>
                <Box sx={{ marginInline: '6px' }}>-</Box>
                <TextField
                  fullWidth
                  variant="standard"
                  multiline
                  value={compEmailTemplateValues?.completed?.[key] || ''}
                  onChange={(e) =>
                    setCompEmailTemplateValues((prev) => ({
                      completed: {
                        ...prev.completed,
                        [key]: e.target.value,
                      },
                    }))
                  }
                  sx={{
                    '& .MuiInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                />
                <Button
                  variant="text"
                  color="primary"
                  sx={{ minWidth: '120px', ml: 1 }}
                  onClick={() => {
                    setCompEmailTemplateValues((prev) => ({
                      completed: {
                        ...prev.completed,
                        [key]:
                          adminData?.compEmailTemplates?.completed?.[key] || '',
                      },
                    }));
                  }}
                >
                  Set Default
                </Button>
              </Box>
            ))}

            {/* Prompt Templates */}
            <Typography
              variant="subtitle1"
              gutterBottom
              color={isDarkMode ? '#90CAF9' : '#1976d2'}
              sx={{ mt: 4 }}
            >
              Prompt Templates
            </Typography>
            {Object.entries(configPromptTemplates).map(([key, label]) => (
              <Box
                key={key}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  mb: 1,
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: '600',
                    color: isDarkMode ? '#e2e8f0' : '#2e2e2e',
                    minWidth: '170px',
                  }}
                >
                  {label}
                </Typography>
                <Box sx={{ marginInline: '6px' }}>-</Box>
                <TextField
                  fullWidth
                  variant="standard"
                  multiline
                  value={promptTemplateValues[key] || ''}
                  onChange={(e) =>
                    handleInputChange(
                      setPromptTemplateValues,
                      key,
                      e.target.value,
                    )
                  }
                  sx={{
                    '& .MuiInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    },
                  }}
                />
                <Button
                  variant="text"
                  color="primary"
                  sx={{ minWidth: '120px', ml: 1 }}
                  onClick={() => {
                    handleInputChange(
                      setPromptTemplateValues,
                      key,
                      adminData?.promptTemplates?.[key] || '',
                    );
                  }}
                >
                  Set Default
                </Button>
              </Box>
            ))}

            {/* Save Configuration Button */}
            <Button
              variant="contained"
              color="primary"
              onClick={handleSave}
              disabled={loading}
              startIcon={<SaveIcon sx={{ color: '#ffffff' }} />}
              sx={{
                mt: 4,
                boxShadow: isDarkMode
                  ? '0px 2px 4px rgba(0,0,0,0.3)'
                  : '0px 1px 2px rgba(0,0,0,0.1)',
                backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                color: '#ffffff',
                '&:hover': {
                  boxShadow: isDarkMode
                    ? '0px 3px 6px rgba(0,0,0,0.4)'
                    : '0px 2px 4px rgba(0,0,0,0.2)',
                  backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8',
                },
                transition: 'all 0.2s ease-in-out',
                fontWeight: 500,
                textTransform: 'none',
                borderRadius: '4px',
              }}
            >
              {loading ? 'Saving...' : 'Save Configuration'}
            </Button>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default AdvancedSettingsTab;
