<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UUID Library Browser Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; border-bottom: 3px solid #007acc; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007acc;
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            border: 1px solid #c3e6c3;
        }
        .blocked {
            background: #ffe8e8;
            border: 1px solid #ffb3b3;
        }
        .allowed {
            background: #e8f5e8;
            border: 1px solid #b3ffb3;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #005a99; }
        .status { font-weight: bold; }
        .allowed-status { color: #28a745; }
        .blocked-status { color: #dc3545; }
        .code {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 UUID Library Browser Demo</h1>
        
        <div class="success">
            <strong>✅ Browser Support Confirmed!</strong><br>
            The uuid library works perfectly in modern browsers (Chrome, Safari, Firefox, Edge).
            It uses the browser's built-in <code>crypto.getRandomValues()</code> API.
        </div>

        <div class="demo-section">
            <h2>📝 UUID Generation Test</h2>
            <p>Click the buttons below to generate different UUID versions:</p>
            
            <button onclick="generateUUID4()">Generate UUID4 (Random)</button>
            <button onclick="generateUUID5()">Generate UUID5 (Name-based)</button>
            <button onclick="generateUUID7()">Generate UUID7 (Time-based)</button>
            <button onclick="simulateUUID8()">Simulate UUID8 (Experimental)</button>
            
            <div id="generation-results"></div>
        </div>

        <div class="demo-section">
            <h2>🔍 UUID Validation Test</h2>
            <p>Test the UUID5-only validation logic:</p>
            
            <button onclick="testValidation()">Run Validation Tests</button>
            <button onclick="testYourUUID()">Test Your Original UUID7</button>
            <button onclick="clearResults()">Clear Results</button>
            
            <div id="validation-results"></div>
        </div>

        <div class="demo-section">
            <h2>🌐 PostHog URL Simulation</h2>
            <p>Simulate how different UUIDs would behave in your PostHog tracking:</p>
            
            <button onclick="simulateURLs()">Simulate URL Scenarios</button>
            
            <div id="url-results"></div>
        </div>

        <div class="demo-section">
            <h2>💻 Browser Compatibility</h2>
            <div id="browser-info"></div>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> Your current validation only allows UUID5. 
            All other UUID versions (including your original UUID7) will be blocked.
        </div>
    </div>

    <!-- Load UUID library from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidv4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidv5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidv7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidvalidate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uuid@10.0.0/dist/umd/uuidversion.min.js"></script>

    <script>
        // Your PostHog validation function (browser version)
        function isValidUUID5Only(uuid) {
            if (!uuid || typeof uuid !== 'string') return true;
            
            // Use uuid library's validation
            if (!uuidvalidate(uuid)) {
                return true; // Allow non-UUID strings
            }
            
            try {
                const version = uuidversion(uuid);
                
                if (version === 5) {
                    console.log('Valid UUID5 detected - allowing tracking:', uuid);
                    return true;
                }
                
                console.log(`UUID version ${version} detected - blocking tracking (only UUID5 allowed):`, uuid);
                return false;
            } catch (error) {
                console.error('Error checking UUID version:', error);
                return true;
            }
        }

        // Generate UUID4
        function generateUUID4() {
            const uuid = uuidv4();
            const isAllowed = isValidUUID5Only(uuid);
            addResult('generation-results', `
                <strong>UUID4 Generated:</strong><br>
                UUID: <code>${uuid}</code><br>
                Version: 4 (Random)<br>
                <span class="status ${isAllowed ? 'allowed-status' : 'blocked-status'}">
                    PostHog Tracking: ${isAllowed ? '✅ ALLOWED' : '❌ BLOCKED'}
                </span>
            `, isAllowed ? 'allowed' : 'blocked');
        }

        // Generate UUID5
        function generateUUID5() {
            const namespace = '1b671a64-40d5-491e-99b0-da01ff1f3341';
            const name = 'browser-test-campaign-' + Date.now();
            const uuid = uuidv5(name, namespace);
            const isAllowed = isValidUUID5Only(uuid);
            addResult('generation-results', `
                <strong>UUID5 Generated:</strong><br>
                UUID: <code>${uuid}</code><br>
                Name: "${name}"<br>
                Version: 5 (Name-based)<br>
                <span class="status ${isAllowed ? 'allowed-status' : 'blocked-status'}">
                    PostHog Tracking: ${isAllowed ? '✅ ALLOWED' : '❌ BLOCKED'}
                </span>
            `, isAllowed ? 'allowed' : 'blocked');
        }

        // Generate UUID7
        function generateUUID7() {
            const uuid = uuidv7();
            const isAllowed = isValidUUID5Only(uuid);
            addResult('generation-results', `
                <strong>UUID7 Generated:</strong><br>
                UUID: <code>${uuid}</code><br>
                Version: 7 (Time-based)<br>
                <span class="status ${isAllowed ? 'allowed-status' : 'blocked-status'}">
                    PostHog Tracking: ${isAllowed ? '✅ ALLOWED' : '❌ BLOCKED'}
                </span>
            `, isAllowed ? 'allowed' : 'blocked');
        }

        // Simulate UUID8
        function simulateUUID8() {
            const uuid = '0198c3db-6e26-8894-aea5-3dc516a689c0'; // Simulated UUID8
            const isAllowed = isValidUUID5Only(uuid);
            addResult('generation-results', `
                <strong>UUID8 Simulated:</strong><br>
                UUID: <code>${uuid}</code><br>
                Version: 8 (Experimental)<br>
                <span class="status ${isAllowed ? 'allowed-status' : 'blocked-status'}">
                    PostHog Tracking: ${isAllowed ? '✅ ALLOWED' : '❌ BLOCKED'}
                </span>
            `, isAllowed ? 'allowed' : 'blocked');
        }

        // Test validation with various UUIDs
        function testValidation() {
            const testCases = [
                { name: 'UUID5', uuid: uuidv5('test', '1b671a64-40d5-491e-99b0-da01ff1f3341'), expected: true },
                { name: 'UUID4', uuid: uuidv4(), expected: false },
                { name: 'UUID7', uuid: uuidv7(), expected: false },
                { name: 'Non-UUID', uuid: 'campaign-123', expected: true },
                { name: 'Empty', uuid: '', expected: true }
            ];

            let results = '<h3>Validation Test Results:</h3>';
            testCases.forEach(test => {
                const result = isValidUUID5Only(test.uuid);
                const success = result === test.expected;
                results += `
                    <div class="result ${result ? 'allowed' : 'blocked'}">
                        <strong>${test.name}:</strong> ${test.uuid}<br>
                        Expected: ${test.expected ? 'ALLOWED' : 'BLOCKED'}<br>
                        Result: ${result ? 'ALLOWED' : 'BLOCKED'} ${success ? '✅' : '❌'}
                    </div>
                `;
            });

            document.getElementById('validation-results').innerHTML = results;
        }

        // Test your original UUID7
        function testYourUUID() {
            const yourUuid = '0198c3db-6e26-7894-aea5-3dc516a689c0';
            const isAllowed = isValidUUID5Only(yourUuid);
            addResult('validation-results', `
                <h3>Your Original UUID Test:</h3>
                <strong>UUID:</strong> <code>${yourUuid}</code><br>
                <strong>Type:</strong> UUID7 (Unix time-based)<br>
                <strong>Version:</strong> ${uuidversion(yourUuid)}<br>
                <span class="status ${isAllowed ? 'allowed-status' : 'blocked-status'}">
                    <strong>PostHog Tracking: ${isAllowed ? '✅ ALLOWED' : '❌ BLOCKED'}</strong>
                </span><br>
                <em>Note: This UUID will be blocked by your new UUID5-only validation.</em>
            `, isAllowed ? 'allowed' : 'blocked');
        }

        // Simulate URL scenarios
        function simulateURLs() {
            const scenarios = [
                { name: 'UUID5 in campaign_id', uuid: uuidv5('url-test', '1b671a64-40d5-491e-99b0-da01ff1f3341') },
                { name: 'UUID4 in uuid param', uuid: uuidv4() },
                { name: 'Your UUID7 in utm_uuid', uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0' },
                { name: 'Non-UUID in n_uuid', uuid: 'summer-campaign' }
            ];

            let results = '<h3>URL Simulation Results:</h3>';
            scenarios.forEach(scenario => {
                const isAllowed = isValidUUID5Only(scenario.uuid);
                results += `
                    <div class="result ${isAllowed ? 'allowed' : 'blocked'}">
                        <strong>${scenario.name}:</strong><br>
                        URL: <code>/audit/product?param=${scenario.uuid}</code><br>
                        <span class="status ${isAllowed ? 'allowed-status' : 'blocked-status'}">
                            Tracking: ${isAllowed ? '✅ ALLOWED' : '❌ BLOCKED'}
                        </span>
                    </div>
                `;
            });

            document.getElementById('url-results').innerHTML = results;
        }

        // Check browser compatibility
        function checkBrowserCompatibility() {
            const info = {
                userAgent: navigator.userAgent,
                cryptoSupport: typeof crypto !== 'undefined' && typeof crypto.getRandomValues === 'function',
                uuidSupport: typeof uuidv4 !== 'undefined' && typeof uuidv5 !== 'undefined',
                validationSupport: typeof uuidvalidate !== 'undefined' && typeof uuidversion !== 'undefined'
            };

            document.getElementById('browser-info').innerHTML = `
                <div class="result allowed">
                    <strong>Browser:</strong> ${navigator.userAgent.split(' ').pop()}<br>
                    <strong>Crypto API:</strong> ${info.cryptoSupport ? '✅ Supported' : '❌ Not supported'}<br>
                    <strong>UUID Generation:</strong> ${info.uuidSupport ? '✅ Working' : '❌ Failed'}<br>
                    <strong>UUID Validation:</strong> ${info.validationSupport ? '✅ Working' : '❌ Failed'}<br>
                    <strong>Overall Status:</strong> ${info.cryptoSupport && info.uuidSupport && info.validationSupport ? '✅ Fully Compatible' : '⚠️ Issues detected'}
                </div>
            `;
        }

        // Helper functions
        function addResult(containerId, content, className = '') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${className}`;
            div.innerHTML = content;
            container.appendChild(div);
        }

        function clearResults() {
            document.getElementById('validation-results').innerHTML = '';
            document.getElementById('generation-results').innerHTML = '';
            document.getElementById('url-results').innerHTML = '';
        }

        // Initialize on page load
        window.onload = function() {
            checkBrowserCompatibility();
            console.log('UUID Browser Demo loaded successfully!');
            console.log('Available functions:', { uuidv4, uuidv5, uuidv7, uuidvalidate, uuidversion });
        };
    </script>
</body>
</html>
