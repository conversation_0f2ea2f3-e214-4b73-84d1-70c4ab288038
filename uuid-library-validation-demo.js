/**
 * UUID Library Validation Demo
 * 
 * This demonstrates the built-in validation capabilities of the uuid library
 * including validate() and version() functions for different UUID types.
 * 
 * Based on official uuid library documentation:
 * https://www.npmjs.com/package/uuid
 */

const { validate: uuidValidate, version: uuidVersion, v4: uuidv4, v5: uuidv5, v7: uuidv7 } = require('uuid');

console.log('🔧 UUID Library Built-in Validation Demo\n');
console.log('=' .repeat(60));

// Test UUIDs
const testUuids = [
  { name: 'Your UUID (UUID7)', uuid: '0198c3db-6e26-7894-aea5-3dc516a689c0' },
  { name: 'Generated UUID4', uuid: uuidv4() },
  { name: 'Generated UUID5', uuid: uuidv5('test-campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341') },
  { name: 'Generated UUID7', uuid: uuidv7() },
  { name: 'Invalid UUID', uuid: 'not-a-uuid' },
  { name: 'Partial UUID', uuid: '0198c3db-6e26-7894' },
  { name: 'NIL UUID', uuid: '00000000-0000-0000-0000-000000000000' },
  { name: 'MAX UUID', uuid: 'ffffffff-ffff-ffff-ffff-ffffffffffff' },
  // Simulated UUID8 (experimental)
  { name: 'Simulated UUID8', uuid: '0198c3db-6e26-8894-aea5-3dc516a689c0' }
];

console.log('\n📝 Testing UUID Library Validation Functions:\n');

testUuids.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}:`);
  console.log(`   UUID: ${test.uuid}`);
  
  // Test validate() function
  const isValid = uuidValidate(test.uuid);
  console.log(`   validate(): ${isValid ? '✅ Valid' : '❌ Invalid'}`);
  
  if (isValid) {
    try {
      // Test version() function
      const version = uuidVersion(test.uuid);
      console.log(`   version(): ${version} (UUID${version})`);
      
      // Special cases
      if (version === 0) {
        console.log(`   Note: NIL UUID (all zeros)`);
      } else if (version === 15) {
        console.log(`   Note: MAX UUID (all ones)`);
      } else if (version === 8) {
        console.log(`   Note: Experimental/vendor-specific UUID`);
      }
    } catch (error) {
      console.log(`   version(): Error - ${error.message}`);
    }
  }
  
  console.log('');
});

// Demonstrate per-version validation as shown in docs
console.log('\n🎯 Per-Version Validation Examples (from UUID docs):\n');

// UUID4 validator function (from docs)
function uuidValidateV4(uuid) {
  return uuidValidate(uuid) && uuidVersion(uuid) === 4;
}

// UUID5 validator function
function uuidValidateV5(uuid) {
  return uuidValidate(uuid) && uuidVersion(uuid) === 5;
}

// UUID7 validator function
function uuidValidateV7(uuid) {
  return uuidValidate(uuid) && uuidVersion(uuid) === 7;
}

// UUID8 validator function (for blocking)
function uuidValidateV8(uuid) {
  return uuidValidate(uuid) && uuidVersion(uuid) === 8;
}

const validators = [
  { name: 'UUID4 Validator', fn: uuidValidateV4 },
  { name: 'UUID5 Validator', fn: uuidValidateV5 },
  { name: 'UUID7 Validator', fn: uuidValidateV7 },
  { name: 'UUID8 Validator', fn: uuidValidateV8 }
];

// Test each validator against your UUID
const yourUuid = '0198c3db-6e26-7894-aea5-3dc516a689c0';
console.log(`Testing validators against your UUID: ${yourUuid}\n`);

validators.forEach(validator => {
  const result = validator.fn(yourUuid);
  console.log(`${validator.name}: ${result ? '✅ Match' : '❌ No match'}`);
});

// Enhanced PostHog validation using UUID library
console.log('\n🚀 Enhanced PostHog Validation Using UUID Library:\n');

/**
 * Enhanced validation function using the uuid library
 * This is more robust than manual parsing
 */
function enhancedPostHogValidation(uuid) {
  // First check if it's a valid UUID at all
  if (!uuidValidate(uuid)) {
    console.log(`   ✅ Non-UUID string - allowing: ${uuid}`);
    return true; // Allow non-UUID strings
  }
  
  try {
    const version = uuidVersion(uuid);
    
    if (version === 8) {
      console.log(`   ❌ UUID8 detected - blocking: ${uuid}`);
      return false; // Block UUID8
    }
    
    console.log(`   ✅ UUID${version} detected - allowing: ${uuid}`);
    return true; // Allow all other versions
  } catch (error) {
    console.log(`   ⚠️  Error checking version - allowing by default: ${error.message}`);
    return true; // Allow on error to prevent breaking functionality
  }
}

// Test enhanced validation
const enhancedTestCases = [
  '0198c3db-6e26-7894-aea5-3dc516a689c0', // Your UUID7
  '0198c3db-6e26-8894-aea5-3dc516a689c0', // Simulated UUID8
  uuidv4(), // Random UUID4
  uuidv5('campaign', '1b671a64-40d5-491e-99b0-da01ff1f3341'), // UUID5
  'campaign-123', // Non-UUID string
  'not-a-uuid', // Invalid string
  '00000000-0000-0000-0000-000000000000' // NIL UUID
];

console.log('Testing enhanced PostHog validation:\n');

enhancedTestCases.forEach((testUuid, index) => {
  console.log(`${index + 1}. Testing: ${testUuid}`);
  const result = enhancedPostHogValidation(testUuid);
  console.log(`   Result: ${result ? 'TRACKING ALLOWED' : 'TRACKING BLOCKED'}\n`);
});

// Updated PostHog validation for your implementation
console.log('\n💡 Recommended Updated PostHog Validation:\n');

console.log(`
// Enhanced validation using uuid library
import { validate as uuidValidate, version as uuidVersion } from 'uuid';

export const isValidUUID5Enhanced = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return true;
  
  // Use uuid library's built-in validation
  if (!uuidValidate(uuid)) {
    return true; // Allow non-UUID strings
  }
  
  try {
    const version = uuidVersion(uuid);
    
    if (version === 8) {
      console.log('UUID8 detected - skipping tracking:', uuid);
      return false; // Block UUID8
    }
    
    console.log(\`UUID version \${version} detected - allowing tracking:\`, uuid);
    return true; // Allow all other versions
  } catch (error) {
    console.error('Error checking UUID version:', error);
    return true; // Allow on error
  }
};
`);

console.log('\n📊 Summary of UUID Library Validation Capabilities:\n');

console.log('✅ Built-in Functions:');
console.log('• validate(str) - Tests if string is a valid UUID format');
console.log('• version(str) - Returns the RFC version number (1-8, 0 for NIL, 15 for MAX)');
console.log('• Throws TypeError if UUID is invalid when calling version()');
console.log('• Works with all UUID versions including experimental UUID8');

console.log('\n✅ Benefits over Manual Parsing:');
console.log('• More robust and RFC-compliant validation');
console.log('• Handles edge cases (NIL, MAX UUIDs)');
console.log('• Maintained by the UUID library team');
console.log('• Less prone to bugs than manual regex/parsing');
console.log('• Supports all current and future UUID versions');

console.log('\n✅ Perfect for Your Use Case:');
console.log('• Can easily detect and block UUID8');
console.log('• Allows all other UUID versions (1-7)');
console.log('• Handles non-UUID strings gracefully');
console.log('• More reliable than manual version extraction');

console.log('\n' + '=' .repeat(60));
console.log('✨ Demo complete! The uuid library has excellent built-in validation.');
