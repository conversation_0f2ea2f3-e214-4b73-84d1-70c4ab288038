{"name": "thefront-js--nextjs", "author": "maccarian", "email": "<EMAIL>", "version": "5.0.0", "private": true, "engines": {"node": ">=20.11.0"}, "scripts": {"dev": "next dev", "build": "next build", "nbuild": "next build", "start": "next start", "prettier:fix": "prettier './src/**/*.js' --write", "eslint:fix": "eslint ./src --fix", "codeStyle:fix": "npm run prettier:fix && npm run eslint:fix", "clear-all": "rimraf ./node_modules ./.next"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.5%", "last 2 versions", "Firefox ESR", "not dead", "not IE 11", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.9", "@mui/material": "^6.4.9", "@mui/styles": "^6.4.8", "@next/third-parties": "^15.2.3", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@vercel/analytics": "^1.5.0", "aos": "^2.3.4", "axios": "^1.8.4", "card-validator": "^8.1.1", "date-fns": "^3.6.0", "formik": "^2.4.6", "jarallax": "^2.2.1", "js-cookie": "^3.0.5", "json-2-csv": "^5.5.9", "markdown-to-jsx": "^7.7.4", "mixpanel-browser": "^2.61.2", "next": "^14.2.25", "posthog-js": "^1.255.1", "posthog-node": "^5.1.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-player": "^2.16.0", "react-slick": "^0.29.0", "react-swipeable": "^7.0.2", "react-syntax-highlighter": "^15.6.1", "slick-carousel": "^1.8.1", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-syntax-flow": "^7.26.0", "@babel/plugin-transform-react-jsx": "^7.25.9", "@tanstack/eslint-plugin-query": "^5.68.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.4", "prettier": "^2.8.8", "rimraf": "^5.0.10", "typescript": "^5.8.2", "yarn-upgrade-all": "^0.7.5"}}