const { csv2json } = require('json-2-csv');
const fs = require('fs');

const filePath = './frontend fields - Final Jeff data.csv';

// Read the CSV file
fs.readFile(filePath, 'utf8', async (err, csvData) => {
  if (err) {
    console.error('Error reading the CSV file:', err);
    return;
  }

  // Convert CSV to JSON
  let jsonData = csv2json(csvData, {});

  jsonData = jsonData.map((obj, idx) => {
    return {
      id: idx + 1,
      company_name: obj['Company name'],
      logo_url: obj['Logo'],
      company_category: obj['Category'].replace(/\s*\(Department:.*?\)/, ''),
      email1: obj['Competitor email'],
    };
  });
  // Write the JSON data to a file
  fs.writeFile(
    '../src/views/NewDemo/data/company_data.json',
    JSON.stringify(jsonData, null, 2),
    'utf8',
    (err) => {
      if (err) {
        console.error('Error writing JSON file:', err);
        return;
      }

      console.log(
        'CSV successfully converted to JSON and saved as src/views/NewDemo/data/company_data.json',
      );
    },
  );
});
